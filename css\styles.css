/* Base styles */
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Open+Sans:wght@300;400;600;700&display=swap');

:root {
  --primary-color: #0056b3;
  --secondary-color: #ffd700;
  --text-color: #333;
  --light-bg: #f8f9fa;
  --white: #ffffff;
  --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.15);
  --shadow-heavy: 0 8px 30px rgba(0, 0, 0, 0.2);
  --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --border-radius: 12px;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Open Sans', sans-serif;
  line-height: 1.6;
  color: var(--text-color);
  background-color: var(--white);
  overflow-x: hidden;
}

/* Header */
header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 5%;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-light);
  position: fixed;
  width: 100%;
  z-index: 1000;
  transition: var(--transition-smooth);
}

.logo {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--primary-color);
  font-family: 'Roboto', sans-serif;
}

nav ul {
  display: flex;
  list-style: none;
}

nav ul li {
  margin-left: 2rem;
}

nav ul li a {
  text-decoration: none;
  color: var(--text-color);
  font-weight: 600;
  transition: var(--transition-smooth);
  position: relative;
  padding: 0.5rem 0;
}

nav ul li a:hover {
  color: var(--primary-color);
}

nav ul li a::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 0;
  left: 0;
  background-color: var(--primary-color);
  transition: var(--transition-smooth);
}

nav ul li a:hover::after {
  width: 100%;
}

/* Hero section */
.hero {
  height: 100vh;
  display: flex;
  align-items: center;
  padding: 0 5%;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.hero-content {
  width: 50%;
  z-index: 2;
  animation: slideInLeft 1s ease-out;
}

.hero h1 {
  font-size: 3.5rem;
  margin-bottom: 1rem;
  font-family: 'Roboto', sans-serif;
  font-weight: 700;
  color: var(--primary-color);
  line-height: 1.2;
}

.hero h2 {
  font-size: 2rem;
  margin-bottom: 1rem;
  color: var(--secondary-color);
  font-weight: 600;
}

.hero p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  color: var(--text-color);
  max-width: 500px;
}
 / *   G l o b e   c o n t a i n e r   * / 
 
 . g l o b e - c o n t a i n e r   { 
 
     w i d t h :   5 0 % ; 
 
     h e i g h t :   1 0 0 % ; 
 
     p o s i t i o n :   r e l a t i v e ; 
 
 } 
 
 
 
 . g l o b e - c o n t a i n e r   c a n v a s   { 
 
     b o r d e r - r a d i u s :   v a r ( - - b o r d e r - r a d i u s ) ; 
 
     b o x - s h a d o w :   v a r ( - - s h a d o w - m e d i u m ) ; 
 
 } 
 
 
 
 / *   I n f o   p o p u p   f o r   g l o b e   * / 
 
 . i n f o - p o p u p   { 
 
     p o s i t i o n :   a b s o l u t e ; 
 
     b a c k g r o u n d :   v a r ( - - p r i m a r y - c o l o r ) ; 
 
     c o l o r :   w h i t e ; 
 
     p a d d i n g :   1 r e m   1 . 5 r e m ; 
 
     b o r d e r - r a d i u s :   v a r ( - - b o r d e r - r a d i u s ) ; 
 
     f o n t - w e i g h t :   6 0 0 ; 
 
     b o x - s h a d o w :   v a r ( - - s h a d o w - h e a v y ) ; 
 
     z - i n d e x :   1 0 ; 
 
     a n i m a t i o n :   p o p u p F a d e I n   0 . 5 s   e a s e - o u t ; 
 
     m a x - w i d t h :   3 0 0 p x ; 
 
     t e x t - a l i g n :   c e n t e r ; 
 
 } 
 
 
 
 . i n f o - p o p u p . f a d e - o u t   { 
 
     a n i m a t i o n :   p o p u p F a d e O u t   1 s   e a s e - o u t   f o r w a r d s ; 
 
 } 
 
 
 
 / *   S e c t i o n s   * / 
 
 . i n f o - s e c t i o n , 
 
 . c a m p u s - s e c t i o n , 
 
 . w h y - f e f u - s e c t i o n , 
 
 . a p p l y - s e c t i o n   { 
 
     p a d d i n g :   5 r e m   5 %   3 r e m ; 
 
     t e x t - a l i g n :   c e n t e r ; 
 
 } 
 
 
 
 . i n f o - s e c t i o n   h 2 , 
 
 . c a m p u s - s e c t i o n   h 2 , 
 
 . w h y - f e f u - s e c t i o n   h 2 , 
 
 . a p p l y - s e c t i o n   h 2   { 
 
     f o n t - s i z e :   2 . 5 r e m ; 
 
     m a r g i n - b o t t o m :   1 r e m ; 
 
     c o l o r :   v a r ( - - p r i m a r y - c o l o r ) ; 
 
     f o n t - f a m i l y :   ' R o b o t o ' ,   s a n s - s e r i f ; 
 
     f o n t - w e i g h t :   7 0 0 ; 
 
 } 
 
 
 
 . i n f o - s e c t i o n   p , 
 
 . c a m p u s - s e c t i o n   p , 
 
 . w h y - f e f u - s e c t i o n   p , 
 
 . a p p l y - s e c t i o n   p   { 
 
     f o n t - s i z e :   1 . 1 r e m ; 
 
     m a r g i n - b o t t o m :   3 r e m ; 
 
     m a x - w i d t h :   8 0 0 p x ; 
 
     m a r g i n - l e f t :   a u t o ; 
 
     m a r g i n - r i g h t :   a u t o ; 
 
     c o l o r :   # 6 6 6 ; 
 
 } 
 
 
 
 / *   3 D   I n f o   C a r d s   * / 
 
 . i n f o - c a r d s - c o n t a i n e r   { 
 
     d i s p l a y :   g r i d ; 
 
     g r i d - t e m p l a t e - c o l u m n s :   r e p e a t ( a u t o - f i t ,   m i n m a x ( 3 0 0 p x ,   1 f r ) ) ; 
 
     g a p :   2 r e m ; 
 
     m a r g i n - t o p :   3 r e m ; 
 
     p e r s p e c t i v e :   1 0 0 0 p x ; 
 
 } 
 
 
 
 . i n f o - c a r d   { 
 
     h e i g h t :   3 0 0 p x ; 
 
     p o s i t i o n :   r e l a t i v e ; 
 
     t r a n s f o r m - s t y l e :   p r e s e r v e - 3 d ; 
 
     t r a n s i t i o n :   t r a n s f o r m   0 . 6 s ; 
 
     c u r s o r :   p o i n t e r ; 
 
 } 
 
 
 
 . i n f o - c a r d : h o v e r   { 
 
     t r a n s f o r m :   r o t a t e Y ( 1 8 0 d e g ) ; 
 
 } 
 
 
 
 . i n f o - c a r d - f r o n t , 
 
 . i n f o - c a r d - b a c k   { 
 
     p o s i t i o n :   a b s o l u t e ; 
 
     w i d t h :   1 0 0 % ; 
 
     h e i g h t :   1 0 0 % ; 
 
     b a c k f a c e - v i s i b i l i t y :   h i d d e n ; 
 
     b o r d e r - r a d i u s :   v a r ( - - b o r d e r - r a d i u s ) ; 
 
     d i s p l a y :   f l e x ; 
 
     f l e x - d i r e c t i o n :   c o l u m n ; 
 
     j u s t i f y - c o n t e n t :   c e n t e r ; 
 
     a l i g n - i t e m s :   c e n t e r ; 
 
     p a d d i n g :   2 r e m ; 
 
     b o x - s h a d o w :   v a r ( - - s h a d o w - m e d i u m ) ; 
 
 } 
 
 
 
 . i n f o - c a r d - f r o n t   { 
 
     b a c k g r o u n d :   l i n e a r - g r a d i e n t ( 1 3 5 d e g ,   v a r ( - - p r i m a r y - c o l o r ) ,   # 0 0 4 1 a 3 ) ; 
 
     c o l o r :   w h i t e ; 
 
 } 
 
 
 
 . i n f o - c a r d - b a c k   { 
 
     b a c k g r o u n d :   w h i t e ; 
 
     c o l o r :   v a r ( - - t e x t - c o l o r ) ; 
 
     t r a n s f o r m :   r o t a t e Y ( 1 8 0 d e g ) ; 
 
     b o r d e r :   2 p x   s o l i d   v a r ( - - p r i m a r y - c o l o r ) ; 
 
 } 
 
 
 
 . i n f o - c a r d - f r o n t   h 3   { 
 
     f o n t - s i z e :   1 . 5 r e m ; 
 
     m a r g i n - b o t t o m :   1 r e m ; 
 
     f o n t - w e i g h t :   7 0 0 ; 
 
 } 
 
 
 
 . i n f o - c a r d - f r o n t   . c a r d - i c o n   { 
 
     f o n t - s i z e :   3 r e m ; 
 
     m a r g i n - b o t t o m :   1 r e m ; 
 
     c o l o r :   v a r ( - - s e c o n d a r y - c o l o r ) ; 
 
 } 
 
 
 
 . i n f o - c a r d - b a c k   h 4   { 
 
     f o n t - s i z e :   1 . 3 r e m ; 
 
     m a r g i n - b o t t o m :   1 r e m ; 
 
     c o l o r :   v a r ( - - p r i m a r y - c o l o r ) ; 
 
     f o n t - w e i g h t :   6 0 0 ; 
 
 } 
 
 
 
 . i n f o - c a r d - b a c k   p   { 
 
     f o n t - s i z e :   1 r e m ; 
 
     l i n e - h e i g h t :   1 . 5 ; 
 
     m a r g i n - b o t t o m :   0 . 5 r e m ; 
 
 } 
 
 
 
 . i n f o - c a r d - b a c k   . h i g h l i g h t   { 
 
     c o l o r :   v a r ( - - p r i m a r y - c o l o r ) ; 
 
     f o n t - w e i g h t :   6 0 0 ; 
 
 } 
 
 
 
 / *   C a m p u s   G a l l e r y   * / 
 
 . c a m p u s - g a l l e r y   { 
 
     h e i g h t :   5 0 0 p x ; 
 
     p o s i t i o n :   r e l a t i v e ; 
 
     m a r g i n :   3 r e m   a u t o ; 
 
     m a x - w i d t h :   1 2 0 0 p x ; 
 
     b o r d e r - r a d i u s :   v a r ( - - b o r d e r - r a d i u s ) ; 
 
     o v e r f l o w :   h i d d e n ; 
 
     b o x - s h a d o w :   v a r ( - - s h a d o w - h e a v y ) ; 
 
 } 
 
 
 
 / *   A d v a n t a g e s   C o n t a i n e r   * / 
 
 . a d v a n t a g e s - c o n t a i n e r   { 
 
     d i s p l a y :   g r i d ; 
 
     g r i d - t e m p l a t e - c o l u m n s :   r e p e a t ( a u t o - f i t ,   m i n m a x ( 2 5 0 p x ,   1 f r ) ) ; 
 
     g a p :   2 r e m ; 
 
     m a r g i n - t o p :   3 r e m ; 
 
     m a x - w i d t h :   1 2 0 0 p x ; 
 
     m a r g i n - l e f t :   a u t o ; 
 
     m a r g i n - r i g h t :   a u t o ; 
 
 } 
 
 
 
 . a d v a n t a g e - i t e m   { 
 
     b a c k g r o u n d :   w h i t e ; 
 
     p a d d i n g :   2 r e m ; 
 
     b o r d e r - r a d i u s :   v a r ( - - b o r d e r - r a d i u s ) ; 
 
     b o x - s h a d o w :   v a r ( - - s h a d o w - m e d i u m ) ; 
 
     t r a n s i t i o n :   v a r ( - - t r a n s i t i o n - s m o o t h ) ; 
 
     t e x t - a l i g n :   c e n t e r ; 
 
     p o s i t i o n :   r e l a t i v e ; 
 
     o v e r f l o w :   h i d d e n ; 
 
 } 
 
 
 
 . a d v a n t a g e - i t e m : h o v e r   { 
 
     t r a n s f o r m :   t r a n s l a t e Y ( - 1 0 p x ) ; 
 
     b o x - s h a d o w :   v a r ( - - s h a d o w - h e a v y ) ; 
 
 } 
 
 
 
 . a d v a n t a g e - i t e m : : b e f o r e   { 
 
     c o n t e n t :   ' ' ; 
 
     p o s i t i o n :   a b s o l u t e ; 
 
     t o p :   0 ; 
 
     l e f t :   - 1 0 0 % ; 
 
     w i d t h :   1 0 0 % ; 
 
     h e i g h t :   1 0 0 % ; 
 
     b a c k g r o u n d :   l i n e a r - g r a d i e n t ( 9 0 d e g ,   t r a n s p a r e n t ,   r g b a ( 2 5 5 ,   2 1 5 ,   0 ,   0 . 1 ) ,   t r a n s p a r e n t ) ; 
 
     t r a n s i t i o n :   l e f t   0 . 5 s ; 
 
 } 
 
 
 
 . a d v a n t a g e - i t e m : h o v e r : : b e f o r e   { 
 
     l e f t :   1 0 0 % ; 
 
 } 
 
 
 
 . a d v a n t a g e - i c o n   { 
 
     w i d t h :   8 0 p x ; 
 
     h e i g h t :   8 0 p x ; 
 
     m a r g i n :   0   a u t o   1 r e m ; 
 
     b a c k g r o u n d :   v a r ( - - p r i m a r y - c o l o r ) ; 
 
     b o r d e r - r a d i u s :   5 0 % ; 
 
     d i s p l a y :   f l e x ; 
 
     a l i g n - i t e m s :   c e n t e r ; 
 
     j u s t i f y - c o n t e n t :   c e n t e r ; 
 
     f o n t - s i z e :   2 r e m ; 
 
     c o l o r :   w h i t e ; 
 
     t r a n s i t i o n :   v a r ( - - t r a n s i t i o n - s m o o t h ) ; 
 
 } 
 
 
 
 . a d v a n t a g e - i t e m : h o v e r   . a d v a n t a g e - i c o n   { 
 
     b a c k g r o u n d :   v a r ( - - s e c o n d a r y - c o l o r ) ; 
 
     c o l o r :   v a r ( - - p r i m a r y - c o l o r ) ; 
 
     t r a n s f o r m :   s c a l e ( 1 . 1 ) ; 
 
 } 
 
 
 
 . a d v a n t a g e - i t e m   h 3   { 
 
     f o n t - s i z e :   1 . 3 r e m ; 
 
     m a r g i n - b o t t o m :   1 r e m ; 
 
     c o l o r :   v a r ( - - p r i m a r y - c o l o r ) ; 
 
     f o n t - w e i g h t :   6 0 0 ; 
 
 } 
 
 
 
 . a d v a n t a g e - i t e m   p   { 
 
     f o n t - s i z e :   1 r e m ; 
 
     c o l o r :   # 6 6 6 ; 
 
     l i n e - h e i g h t :   1 . 5 ; 
 
 } 
 
 
 
 / *   A p p l y   B u t t o n   C o n t a i n e r   * / 
 
 . a p p l y - b u t t o n - c o n t a i n e r   { 
 
     m a r g i n :   3 r e m   a u t o ; 
 
     h e i g h t :   2 0 0 p x ; 
 
     d i s p l a y :   f l e x ; 
 
     a l i g n - i t e m s :   c e n t e r ; 
 
     j u s t i f y - c o n t e n t :   c e n t e r ; 
 
 } 
 
 
 
 / *   A p p l i c a t i o n   F o r m   * / 
 
 . a p p l i c a t i o n - f o r m   { 
 
     m a x - w i d t h :   6 0 0 p x ; 
 
     m a r g i n :   3 r e m   a u t o ; 
 
     b a c k g r o u n d :   w h i t e ; 
 
     p a d d i n g :   3 r e m ; 
 
     b o r d e r - r a d i u s :   v a r ( - - b o r d e r - r a d i u s ) ; 
 
     b o x - s h a d o w :   v a r ( - - s h a d o w - m e d i u m ) ; 
 
 } 
 
 
 
 . a p p l i c a t i o n - f o r m   h 3   { 
 
     f o n t - s i z e :   2 r e m ; 
 
     m a r g i n - b o t t o m :   2 r e m ; 
 
     c o l o r :   v a r ( - - p r i m a r y - c o l o r ) ; 
 
     t e x t - a l i g n :   c e n t e r ; 
 
     f o n t - f a m i l y :   ' R o b o t o ' ,   s a n s - s e r i f ; 
 
 } 
 
 
 
 . f o r m - g r o u p   { 
 
     m a r g i n - b o t t o m :   1 . 5 r e m ; 
 
 } 
 
 
 
 . f o r m - g r o u p   l a b e l   { 
 
     d i s p l a y :   b l o c k ; 
 
     m a r g i n - b o t t o m :   0 . 5 r e m ; 
 
     f o n t - w e i g h t :   6 0 0 ; 
 
     c o l o r :   v a r ( - - t e x t - c o l o r ) ; 
 
 } 
 
 
 
 . f o r m - g r o u p   i n p u t , 
 
 . f o r m - g r o u p   t e x t a r e a   { 
 
     w i d t h :   1 0 0 % ; 
 
     p a d d i n g :   1 r e m ; 
 
     b o r d e r :   2 p x   s o l i d   # e 9 e c e f ; 
 
     b o r d e r - r a d i u s :   v a r ( - - b o r d e r - r a d i u s ) ; 
 
     f o n t - s i z e :   1 r e m ; 
 
     t r a n s i t i o n :   v a r ( - - t r a n s i t i o n - s m o o t h ) ; 
 
     f o n t - f a m i l y :   i n h e r i t ; 
 
 } 
 
 
 
 . f o r m - g r o u p   i n p u t : f o c u s , 
 
 . f o r m - g r o u p   t e x t a r e a : f o c u s   { 
 
     o u t l i n e :   n o n e ; 
 
     b o r d e r - c o l o r :   v a r ( - - p r i m a r y - c o l o r ) ; 
 
     b o x - s h a d o w :   0   0   0   3 p x   r g b a ( 0 ,   8 6 ,   1 7 9 ,   0 . 1 ) ; 
 
 } 
 
 
 
 . s u b m i t - b t n   { 
 
     w i d t h :   1 0 0 % ; 
 
     p a d d i n g :   1 r e m   2 r e m ; 
 
     b a c k g r o u n d :   v a r ( - - p r i m a r y - c o l o r ) ; 
 
     c o l o r :   w h i t e ; 
 
     b o r d e r :   n o n e ; 
 
     b o r d e r - r a d i u s :   v a r ( - - b o r d e r - r a d i u s ) ; 
 
     f o n t - s i z e :   1 . 1 r e m ; 
 
     f o n t - w e i g h t :   6 0 0 ; 
 
     c u r s o r :   p o i n t e r ; 
 
     t r a n s i t i o n :   v a r ( - - t r a n s i t i o n - s m o o t h ) ; 
 
 } 
 
 
 
 . s u b m i t - b t n : h o v e r   { 
 
     b a c k g r o u n d :   # 0 0 4 1 a 3 ; 
 
     t r a n s f o r m :   t r a n s l a t e Y ( - 2 p x ) ; 
 
     b o x - s h a d o w :   v a r ( - - s h a d o w - m e d i u m ) ; 
 
 } 
 
 
 
 / *   F o o t e r   * / 
 
 f o o t e r   { 
 
     b a c k g r o u n d :   v a r ( - - t e x t - c o l o r ) ; 
 
     c o l o r :   w h i t e ; 
 
     p a d d i n g :   3 r e m   5 %   1 r e m ; 
 
 } 
 
 
 
 . f o o t e r - c o n t e n t   { 
 
     d i s p l a y :   g r i d ; 
 
     g r i d - t e m p l a t e - c o l u m n s :   r e p e a t ( a u t o - f i t ,   m i n m a x ( 2 5 0 p x ,   1 f r ) ) ; 
 
     g a p :   2 r e m ; 
 
     m a r g i n - b o t t o m :   2 r e m ; 
 
 } 
 
 
 
 . f o o t e r - s e c t i o n   h 3   { 
 
     f o n t - s i z e :   1 . 3 r e m ; 
 
     m a r g i n - b o t t o m :   1 r e m ; 
 
     c o l o r :   v a r ( - - s e c o n d a r y - c o l o r ) ; 
 
 } 
 
 
 
 . f o o t e r - s e c t i o n   p   { 
 
     m a r g i n - b o t t o m :   0 . 5 r e m ; 
 
     c o l o r :   # c c c ; 
 
 } 
 
 
 
 . s o c i a l - i c o n s   { 
 
     d i s p l a y :   f l e x ; 
 
     g a p :   1 r e m ; 
 
 } 
 
 
 
 . s o c i a l - i c o n s   a   { 
 
     c o l o r :   w h i t e ; 
 
     f o n t - s i z e :   1 . 5 r e m ; 
 
     t r a n s i t i o n :   v a r ( - - t r a n s i t i o n - s m o o t h ) ; 
 
 } 
 
 
 
 . s o c i a l - i c o n s   a : h o v e r   { 
 
     c o l o r :   v a r ( - - s e c o n d a r y - c o l o r ) ; 
 
     t r a n s f o r m :   s c a l e ( 1 . 2 ) ; 
 
 } 
 
 
 
 . c o p y r i g h t   { 
 
     t e x t - a l i g n :   c e n t e r ; 
 
     p a d d i n g - t o p :   2 r e m ; 
 
     b o r d e r - t o p :   1 p x   s o l i d   # 5 5 5 ; 
 
     c o l o r :   # c c c ; 
 
 } 
 
 