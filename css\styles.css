/* Base styles */
:root {
  --primary-color: #0056b3;
  --secondary-color: #ffd700;
  --text-color: #333;
  --light-bg: #f8f9fa;
  --white: #ffffff;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Open Sans', sans-serif;
  line-height: 1.6;
  color: var(--text-color);
  background-color: var(--white);
}

/* Header */
header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 5%;
  background-color: var(--white);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: fixed;
  width: 100%;
  z-index: 1000;
}

.logo {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
}

nav ul {
  display: flex;
  list-style: none;
}

nav ul li {
  margin-left: 2rem;
}

nav ul li a {
  text-decoration: none;
  color: var(--text-color);
  font-weight: 600;
  transition: color 0.3s;
}

nav ul li a:hover {
  color: var(--primary-color);
}

/* Hero section */
.hero {
  height: 100vh;
  display: flex;
  align-items: center;
  padding: 0 5%;
  position: relative;
  overflow: hidden;
}

.hero-content {
  width: 50%;
  z-index: 2;
}

.hero h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
 