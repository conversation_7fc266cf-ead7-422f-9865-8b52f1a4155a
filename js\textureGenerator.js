// Generate a simple earth-like texture for the globe
export function generateEarthTexture() {
  const canvas = document.createElement('canvas');
  canvas.width = 512;
  canvas.height = 256;
  const ctx = canvas.getContext('2d');

  // Create gradient for ocean
  const oceanGradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
  oceanGradient.addColorStop(0, '#4A90E2');
  oceanGradient.addColorStop(0.5, '#2E5BBA');
  oceanGradient.addColorStop(1, '#1E3A8A');

  // Fill with ocean
  ctx.fillStyle = oceanGradient;
  ctx.fillRect(0, 0, canvas.width, canvas.height);

  // Add some simple landmasses
  ctx.fillStyle = '#8B7355';
  
  // Simple continent shapes
  const continents = [
    // Africa-like shape
    { x: 250, y: 100, width: 60, height: 80 },
    // Asia-like shape
    { x: 300, y: 80, width: 100, height: 60 },
    // Europe-like shape
    { x: 240, y: 70, width: 40, height: 30 },
    // Americas-like shape
    { x: 100, y: 90, width: 80, height: 100 },
    // Australia-like shape
    { x: 380, y: 160, width: 40, height: 25 }
  ];

  continents.forEach(continent => {
    ctx.beginPath();
    ctx.ellipse(
      continent.x, 
      continent.y, 
      continent.width / 2, 
      continent.height / 2, 
      0, 0, 2 * Math.PI
    );
    ctx.fill();
  });

  // Add some texture with noise
  const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
  const data = imageData.data;

  for (let i = 0; i < data.length; i += 4) {
    // Add slight noise
    const noise = (Math.random() - 0.5) * 20;
    data[i] = Math.max(0, Math.min(255, data[i] + noise));     // Red
    data[i + 1] = Math.max(0, Math.min(255, data[i + 1] + noise)); // Green
    data[i + 2] = Math.max(0, Math.min(255, data[i + 2] + noise)); // Blue
  }

  ctx.putImageData(imageData, 0, 0);

  return canvas.toDataURL();
}

// Generate a simple normal map for the earth
export function generateEarthNormalMap() {
  const canvas = document.createElement('canvas');
  canvas.width = 512;
  canvas.height = 256;
  const ctx = canvas.getContext('2d');

  // Fill with neutral normal (128, 128, 255)
  ctx.fillStyle = '#8080FF';
  ctx.fillRect(0, 0, canvas.width, canvas.height);

  return canvas.toDataURL();
}
