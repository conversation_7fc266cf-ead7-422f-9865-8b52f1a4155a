// FEFU MBBS Program Information Cards
export function createInfoCards(container) {
  const cardData = [
    {
      id: 'tuition',
      icon: '💰',
      title: 'Tuition Fees',
      frontText: 'Affordable Education',
      details: {
        title: 'Annual Tuition',
        content: [
          '440,000 - 495,000 RUB/year',
          '≈ ₹3.8 - 4.95 Lakhs/year',
          'Includes all academic fees',
          'No hidden charges',
          'Payment plans available'
        ]
      }
    },
    {
      id: 'accommodation',
      icon: '🏠',
      title: 'Accommodation',
      frontText: 'Modern Hostels',
      details: {
        title: 'Hostel Facilities',
        content: [
          '44,000 - 50,000 RUB/year',
          '≈ ₹0.4 - 0.5 Lakhs/year',
          'Furnished rooms',
          'Wi-Fi & utilities included',
          'On-campus security'
        ]
      }
    },
    {
      id: 'living',
      icon: '🍽️',
      title: 'Living Costs',
      frontText: 'Budget-Friendly',
      details: {
        title: 'Monthly Expenses',
        content: [
          '15,000 - 20,000 RUB/month',
          '≈ ₹13,000 - 17,000/month',
          'Food & transportation',
          'Personal expenses',
          'Entertainment & misc.'
        ]
      }
    },
    {
      id: 'eligibility',
      icon: '📚',
      title: 'Eligibility',
      frontText: 'Entry Requirements',
      details: {
        title: 'Admission Criteria',
        content: [
          '50% in Physics, Chemistry, Biology',
          'NEET qualification required',
          'Age: 17-25 years',
          'English proficiency',
          'Medical fitness certificate'
        ]
      }
    },
    {
      id: 'duration',
      icon: '⏱️',
      title: 'Program Duration',
      frontText: '6-Year Program',
      details: {
        title: 'Course Structure',
        content: [
          '6 years total duration',
          '5 years academic study',
          '1 year internship',
          'English-medium instruction',
          'International curriculum'
        ]
      }
    },
    {
      id: 'recognition',
      icon: '🏆',
      title: 'Recognition',
      frontText: 'Global Acceptance',
      details: {
        title: 'Accreditation',
        content: [
          'WHO recognized university',
          'NMC (India) approved',
          'WFME listed institution',
          'Practice globally',
          'Transfer opportunities'
        ]
      }
    }
  ];

  // Create cards container
  container.innerHTML = '';
  
  cardData.forEach((card, index) => {
    const cardElement = createCard(card, index);
    container.appendChild(cardElement);
  });

  // Add intersection observer for staggered animations
  setupCardAnimations(container);

  return {
    cards: cardData,
    container,
    animate: () => animateCards(container)
  };
}

function createCard(cardData, index) {
  const card = document.createElement('div');
  card.className = 'info-card';
  card.setAttribute('data-card-id', cardData.id);
  card.style.animationDelay = `${index * 0.1}s`;

  // Front of the card
  const front = document.createElement('div');
  front.className = 'info-card-front';
  front.innerHTML = `
    <div class="card-icon">${cardData.icon}</div>
    <h3>${cardData.title}</h3>
    <p>${cardData.frontText}</p>
    <div class="card-hint">
      <i class="fas fa-sync-alt"></i>
      <span>Hover to flip</span>
    </div>
  `;

  // Back of the card
  const back = document.createElement('div');
  back.className = 'info-card-back';
  
  const detailsList = cardData.details.content
    .map(item => `<p>${item}</p>`)
    .join('');

  back.innerHTML = `
    <h4>${cardData.details.title}</h4>
    <div class="card-details">
      ${detailsList}
    </div>
    <div class="card-source">
      <small>*Based on official FEFU data</small>
    </div>
  `;

  card.appendChild(front);
  card.appendChild(back);

  // Add touch support for mobile
  if ('ontouchstart' in window) {
    let isFlipped = false;
    
    card.addEventListener('touchstart', (e) => {
      e.preventDefault();
      isFlipped = !isFlipped;
      card.style.transform = isFlipped ? 'rotateY(180deg)' : 'rotateY(0deg)';
    });
  }

  // Add click tracking
  card.addEventListener('click', () => {
    trackCardInteraction(cardData.id);
  });

  return card;
}

function setupCardAnimations(container) {
  const cards = container.querySelectorAll('.info-card');
  
  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry, index) => {
      if (entry.isIntersecting) {
        setTimeout(() => {
          entry.target.classList.add('animate-in');
        }, index * 100);
      }
    });
  }, {
    threshold: 0.2
  });

  cards.forEach(card => {
    observer.observe(card);
  });
}

function animateCards(container) {
  const cards = container.querySelectorAll('.info-card');
  
  cards.forEach((card, index) => {
    setTimeout(() => {
      card.style.animation = 'fadeInUp 0.6s ease-out forwards';
    }, index * 100);
  });
}

function trackCardInteraction(cardId) {
  // Analytics tracking (replace with actual analytics)
  console.log(`Card interaction: ${cardId}`);
  
  // You can integrate with Google Analytics, Mixpanel, etc.
  if (typeof gtag !== 'undefined') {
    gtag('event', 'card_interaction', {
      'card_id': cardId,
      'event_category': 'engagement'
    });
  }
}

// Utility function to update card data (for dynamic content)
export function updateCardData(container, cardId, newData) {
  const card = container.querySelector(`[data-card-id="${cardId}"]`);
  if (!card) return;

  const back = card.querySelector('.info-card-back');
  if (back && newData.details) {
    const detailsList = newData.details.content
      .map(item => `<p>${item}</p>`)
      .join('');

    back.innerHTML = `
      <h4>${newData.details.title}</h4>
      <div class="card-details">
        ${detailsList}
      </div>
      <div class="card-source">
        <small>*Based on official FEFU data</small>
      </div>
    `;
  }
}

// Export additional utilities
export const cardUtils = {
  updateCardData,
  trackCardInteraction
};
