import * as THREE from 'three';

export async function createCampusGallery(container) {
  // Campus images data
  const campusImages = [
    {
      url: 'https://images.unsplash.com/photo-**********-701939374585?w=800&h=600&fit=crop',
      title: 'FEFU Main Campus',
      description: 'Modern campus facilities on Russky Island'
    },
    {
      url: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=800&h=600&fit=crop',
      title: 'Medical Faculty Building',
      description: 'State-of-the-art medical education facilities'
    },
    {
      url: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=800&h=600&fit=crop',
      title: 'Student Dormitories',
      description: 'Comfortable accommodation for international students'
    },
    {
      url: 'https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=800&h=600&fit=crop',
      title: 'Laboratory Facilities',
      description: 'Advanced medical research and training labs'
    },
    {
      url: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&h=600&fit=crop',
      title: 'Library & Study Areas',
      description: 'Extensive medical library and study spaces'
    },
    {
      url: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&h=600&fit=crop',
      title: 'Campus Life',
      description: 'Vibrant student community and activities'
    }
  ];

  // Check if WebGL is supported
  if (!isWebGLSupported()) {
    return createFallbackGallery(container, campusImages);
  }

  try {
    return await create3DGallery(container, campusImages);
  } catch (error) {
    console.warn('3D gallery failed, falling back to 2D:', error);
    return createFallbackGallery(container, campusImages);
  }
}

async function create3DGallery(container, images) {
  // Scene setup
  const scene = new THREE.Scene();
  const camera = new THREE.PerspectiveCamera(75, container.clientWidth / container.clientHeight, 0.1, 1000);
  
  const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
  renderer.setSize(container.clientWidth, container.clientHeight);
  renderer.setClearColor(0x000000, 0);
  container.appendChild(renderer.domElement);

  // Create carousel
  const carousel = new THREE.Group();
  scene.add(carousel);

  const radius = 8;
  const imageWidth = 6;
  const imageHeight = 4;
  const panels = [];

  // Load textures and create panels
  const textureLoader = new THREE.TextureLoader();
  
  for (let i = 0; i < images.length; i++) {
    const angle = (i / images.length) * Math.PI * 2;
    
    try {
      const texture = await loadTexture(textureLoader, images[i].url);
      const panel = createImagePanel(texture, imageWidth, imageHeight, images[i]);
      
      panel.position.x = Math.cos(angle) * radius;
      panel.position.z = Math.sin(angle) * radius;
      panel.rotation.y = -angle;
      
      carousel.add(panel);
      panels.push({ panel, data: images[i], angle });
      
    } catch (error) {
      console.warn(`Failed to load image ${i}:`, error);
    }
  }

  // Camera positioning
  camera.position.set(0, 2, 12);
  camera.lookAt(0, 0, 0);

  // Lighting
  const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
  scene.add(ambientLight);

  const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
  directionalLight.position.set(10, 10, 5);
  scene.add(directionalLight);

  // Animation variables
  let currentRotation = 0;
  let targetRotation = 0;
  let isAutoRotating = true;
  let autoRotateSpeed = 0.005;

  // Controls
  const controls = {
    isMouseDown: false,
    mouseX: 0,
    mouseY: 0,
    startX: 0
  };

  // Event listeners
  setupGalleryControls(container, controls, () => {
    isAutoRotating = false;
  });

  // Raycaster for click detection
  const raycaster = new THREE.Raycaster();
  const mouse = new THREE.Vector2();

  container.addEventListener('click', (event) => {
    mouse.x = (event.clientX / container.clientWidth) * 2 - 1;
    mouse.y = -(event.clientY / container.clientHeight) * 2 + 1;

    raycaster.setFromCamera(mouse, camera);
    const intersects = raycaster.intersectObjects(panels.map(p => p.panel));

    if (intersects.length > 0) {
      const clickedPanel = intersects[0].object;
      const panelData = panels.find(p => p.panel === clickedPanel);
      if (panelData) {
        showImageModal(panelData.data);
      }
    }
  });

  // Animation loop
  function animate() {
    requestAnimationFrame(animate);

    // Auto rotation
    if (isAutoRotating) {
      targetRotation += autoRotateSpeed;
    }

    // Manual rotation
    if (controls.isMouseDown) {
      const deltaX = controls.mouseX - controls.startX;
      targetRotation = currentRotation + deltaX * 0.01;
    }

    // Smooth rotation
    currentRotation += (targetRotation - currentRotation) * 0.05;
    carousel.rotation.y = currentRotation;

    renderer.render(scene, camera);
  }

  animate();

  // Handle resize
  const handleResize = () => {
    camera.aspect = container.clientWidth / container.clientHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(container.clientWidth, container.clientHeight);
  };

  window.addEventListener('resize', handleResize);

  // Navigation controls
  createGalleryNavigation(container, panels.length, (index) => {
    const targetAngle = -(index / panels.length) * Math.PI * 2;
    targetRotation = targetAngle;
    isAutoRotating = false;
    
    setTimeout(() => {
      isAutoRotating = true;
    }, 3000);
  });

  return {
    scene,
    camera,
    renderer,
    carousel,
    panels,
    handleResize,
    destroy: () => {
      window.removeEventListener('resize', handleResize);
      renderer.dispose();
      container.removeChild(renderer.domElement);
    }
  };
}

function createImagePanel(texture, width, height, imageData) {
  const geometry = new THREE.PlaneGeometry(width, height);
  const material = new THREE.MeshLambertMaterial({ 
    map: texture,
    transparent: true
  });
  
  const panel = new THREE.Mesh(geometry, material);
  
  // Add frame
  const frameGeometry = new THREE.PlaneGeometry(width + 0.2, height + 0.2);
  const frameMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
  const frame = new THREE.Mesh(frameGeometry, frameMaterial);
  frame.position.z = -0.01;
  panel.add(frame);

  // Add title text (simplified - in real implementation you'd use a text texture)
  panel.userData = imageData;

  return panel;
}

function loadTexture(loader, url) {
  return new Promise((resolve, reject) => {
    loader.load(
      url,
      resolve,
      undefined,
      reject
    );
  });
}

function setupGalleryControls(container, controls, onInteraction) {
  container.addEventListener('mousedown', (e) => {
    controls.isMouseDown = true;
    controls.startX = e.clientX;
    controls.mouseX = e.clientX;
    onInteraction();
  });

  container.addEventListener('mousemove', (e) => {
    if (controls.isMouseDown) {
      controls.mouseX = e.clientX;
    }
  });

  container.addEventListener('mouseup', () => {
    controls.isMouseDown = false;
  });

  container.addEventListener('mouseleave', () => {
    controls.isMouseDown = false;
  });

  // Touch events
  container.addEventListener('touchstart', (e) => {
    controls.isMouseDown = true;
    controls.startX = e.touches[0].clientX;
    controls.mouseX = e.touches[0].clientX;
    onInteraction();
  });

  container.addEventListener('touchmove', (e) => {
    if (controls.isMouseDown) {
      controls.mouseX = e.touches[0].clientX;
    }
  });

  container.addEventListener('touchend', () => {
    controls.isMouseDown = false;
  });
}

function createGalleryNavigation(container, imageCount, onNavigate) {
  const nav = document.createElement('div');
  nav.className = 'gallery-navigation';
  
  for (let i = 0; i < imageCount; i++) {
    const dot = document.createElement('button');
    dot.className = 'gallery-dot';
    dot.setAttribute('aria-label', `View image ${i + 1}`);
    dot.addEventListener('click', () => onNavigate(i));
    nav.appendChild(dot);
  }
  
  container.appendChild(nav);
}

function showImageModal(imageData) {
  const modal = document.createElement('div');
  modal.className = 'image-modal';
  modal.innerHTML = `
    <div class="modal-content">
      <button class="modal-close">&times;</button>
      <img src="${imageData.url}" alt="${imageData.title}">
      <div class="modal-info">
        <h3>${imageData.title}</h3>
        <p>${imageData.description}</p>
      </div>
    </div>
  `;

  document.body.appendChild(modal);

  const closeBtn = modal.querySelector('.modal-close');
  closeBtn.addEventListener('click', () => {
    modal.remove();
  });

  modal.addEventListener('click', (e) => {
    if (e.target === modal) {
      modal.remove();
    }
  });
}

function createFallbackGallery(container, images) {
  container.innerHTML = `
    <div class="fallback-gallery">
      <div class="gallery-grid">
        ${images.map(img => `
          <div class="gallery-item">
            <img src="${img.url}" alt="${img.title}" loading="lazy">
            <div class="gallery-item-info">
              <h3>${img.title}</h3>
              <p>${img.description}</p>
            </div>
          </div>
        `).join('')}
      </div>
    </div>
  `;

  return {
    type: 'fallback',
    container
  };
}

function isWebGLSupported() {
  try {
    const canvas = document.createElement('canvas');
    return !!(window.WebGLRenderingContext && canvas.getContext('webgl'));
  } catch (e) {
    return false;
  }
}
