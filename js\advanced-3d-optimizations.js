import * as THREE from 'three';

// Advanced Three.js optimization utilities
export class Advanced3DOptimizer {
  constructor(renderer, scene, camera) {
    this.renderer = renderer;
    this.scene = scene;
    this.camera = camera;
    this.frustum = new THREE.Frustum();
    this.cameraMatrix = new THREE.Matrix4();
    this.lodObjects = new Map();
    this.instancedMeshes = new Map();
    this.culledObjects = new Set();
    
    this.setupFrustumCulling();
    this.setupLODSystem();
  }

  // Frustum Culling Implementation
  setupFrustumCulling() {
    this.originalRender = this.renderer.render.bind(this.renderer);
    
    this.renderer.render = (scene, camera) => {
      this.updateFrustum(camera);
      this.performFrustumCulling(scene);
      this.originalRender(scene, camera);
      this.restoreCulledObjects();
    };
  }

  updateFrustum(camera) {
    this.cameraMatrix.multiplyMatrices(camera.projectionMatrix, camera.matrixWorldInverse);
    this.frustum.setFromProjectionMatrix(this.cameraMatrix);
  }

  performFrustumCulling(object) {
    if (object.isMesh || object.isPoints || object.isLine) {
      if (object.geometry && object.geometry.boundingSphere) {
        if (!object.geometry.boundingSphere.center) {
          object.geometry.computeBoundingSphere();
        }
        
        const sphere = object.geometry.boundingSphere.clone();
        sphere.applyMatrix4(object.matrixWorld);
        
        if (!this.frustum.intersectsSphere(sphere)) {
          if (object.visible) {
            object.visible = false;
            this.culledObjects.add(object);
          }
          return;
        }
      }
      
      if (!object.visible && this.culledObjects.has(object)) {
        object.visible = true;
        this.culledObjects.delete(object);
      }
    }

    if (object.children) {
      object.children.forEach(child => this.performFrustumCulling(child));
    }
  }

  restoreCulledObjects() {
    // Objects will be restored in next frame if they come back into view
  }

  // Level of Detail (LOD) System
  setupLODSystem() {
    this.lodLevels = {
      high: { distance: 100, detail: 1.0 },
      medium: { distance: 300, detail: 0.6 },
      low: { distance: 600, detail: 0.3 },
      minimal: { distance: 1000, detail: 0.1 }
    };
  }

  createLODObject(geometry, material, position) {
    const lodGroup = new THREE.LOD();
    
    // High detail
    const highDetail = new THREE.Mesh(geometry, material);
    lodGroup.addLevel(highDetail, 0);
    
    // Medium detail
    const mediumGeometry = this.simplifyGeometry(geometry, 0.6);
    const mediumDetail = new THREE.Mesh(mediumGeometry, material);
    lodGroup.addLevel(mediumDetail, this.lodLevels.medium.distance);
    
    // Low detail
    const lowGeometry = this.simplifyGeometry(geometry, 0.3);
    const lowDetail = new THREE.Mesh(lowGeometry, material);
    lodGroup.addLevel(lowDetail, this.lodLevels.low.distance);
    
    // Minimal detail
    const minimalGeometry = this.simplifyGeometry(geometry, 0.1);
    const minimalDetail = new THREE.Mesh(minimalGeometry, material);
    lodGroup.addLevel(minimalDetail, this.lodLevels.minimal.distance);
    
    if (position) {
      lodGroup.position.copy(position);
    }
    
    this.lodObjects.set(lodGroup.uuid, lodGroup);
    return lodGroup;
  }

  simplifyGeometry(geometry, factor) {
    if (geometry.isBufferGeometry) {
      const simplified = geometry.clone();
      
      // Reduce vertex count based on factor
      const positions = simplified.attributes.position.array;
      const newPositions = new Float32Array(Math.floor(positions.length * factor));
      
      for (let i = 0; i < newPositions.length; i += 3) {
        const sourceIndex = Math.floor((i / newPositions.length) * positions.length);
        newPositions[i] = positions[sourceIndex];
        newPositions[i + 1] = positions[sourceIndex + 1];
        newPositions[i + 2] = positions[sourceIndex + 2];
      }
      
      simplified.setAttribute('position', new THREE.BufferAttribute(newPositions, 3));
      simplified.computeVertexNormals();
      
      return simplified;
    }
    
    return geometry;
  }

  // Instanced Rendering for Repeated Elements
  createInstancedMesh(geometry, material, count, positions) {
    const instancedMesh = new THREE.InstancedMesh(geometry, material, count);
    
    const matrix = new THREE.Matrix4();
    
    positions.forEach((position, index) => {
      matrix.setPosition(position.x, position.y, position.z);
      instancedMesh.setMatrixAt(index, matrix);
    });
    
    instancedMesh.instanceMatrix.needsUpdate = true;
    instancedMesh.computeBoundingSphere();
    
    this.instancedMeshes.set(instancedMesh.uuid, instancedMesh);
    return instancedMesh;
  }

  // Occlusion Culling
  setupOcclusionCulling() {
    this.occlusionQuery = this.renderer.getContext().createQuery();
    this.occludedObjects = new Set();
  }

  // Batch Rendering Optimization
  optimizeBatchRendering() {
    const materialGroups = new Map();
    
    this.scene.traverse((object) => {
      if (object.isMesh) {
        const materialKey = this.getMaterialKey(object.material);
        
        if (!materialGroups.has(materialKey)) {
          materialGroups.set(materialKey, []);
        }
        
        materialGroups.get(materialKey).push(object);
      }
    });
    
    // Sort by material to reduce state changes
    materialGroups.forEach((objects, materialKey) => {
      objects.sort((a, b) => {
        const distanceA = a.position.distanceTo(this.camera.position);
        const distanceB = b.position.distanceTo(this.camera.position);
        return distanceA - distanceB;
      });
    });
  }

  getMaterialKey(material) {
    return `${material.type}_${material.uuid}`;
  }

  // Texture Atlas Management
  createTextureAtlas(textures, atlasSize = 2048) {
    const canvas = document.createElement('canvas');
    canvas.width = atlasSize;
    canvas.height = atlasSize;
    const ctx = canvas.getContext('2d');
    
    const atlas = {
      texture: new THREE.CanvasTexture(canvas),
      uvMappings: new Map()
    };
    
    const tileSize = Math.floor(atlasSize / Math.ceil(Math.sqrt(textures.length)));
    let x = 0, y = 0;
    
    textures.forEach((texture, index) => {
      if (texture.image) {
        ctx.drawImage(texture.image, x, y, tileSize, tileSize);
        
        atlas.uvMappings.set(texture.uuid, {
          offsetX: x / atlasSize,
          offsetY: y / atlasSize,
          scaleX: tileSize / atlasSize,
          scaleY: tileSize / atlasSize
        });
        
        x += tileSize;
        if (x >= atlasSize) {
          x = 0;
          y += tileSize;
        }
      }
    });
    
    atlas.texture.needsUpdate = true;
    return atlas;
  }

  // Memory Pool for Geometry
  createGeometryPool() {
    this.geometryPool = {
      sphere: new Map(),
      box: new Map(),
      plane: new Map(),
      cylinder: new Map()
    };
  }

  getPooledGeometry(type, ...params) {
    const key = `${type}_${params.join('_')}`;
    
    if (this.geometryPool[type].has(key)) {
      return this.geometryPool[type].get(key);
    }
    
    let geometry;
    switch (type) {
      case 'sphere':
        geometry = new THREE.SphereGeometry(...params);
        break;
      case 'box':
        geometry = new THREE.BoxGeometry(...params);
        break;
      case 'plane':
        geometry = new THREE.PlaneGeometry(...params);
        break;
      case 'cylinder':
        geometry = new THREE.CylinderGeometry(...params);
        break;
      default:
        throw new Error(`Unknown geometry type: ${type}`);
    }
    
    this.geometryPool[type].set(key, geometry);
    return geometry;
  }

  // Performance Monitoring
  startPerformanceMonitoring() {
    this.performanceStats = {
      drawCalls: 0,
      triangles: 0,
      points: 0,
      lines: 0,
      frameTime: 0,
      memoryUsage: 0
    };
    
    const originalRender = this.renderer.render.bind(this.renderer);
    
    this.renderer.render = (scene, camera) => {
      const startTime = performance.now();
      
      // Reset stats
      this.performanceStats.drawCalls = 0;
      this.performanceStats.triangles = 0;
      
      originalRender(scene, camera);
      
      this.performanceStats.frameTime = performance.now() - startTime;
      this.performanceStats.drawCalls = this.renderer.info.render.calls;
      this.performanceStats.triangles = this.renderer.info.render.triangles;
      
      if (performance.memory) {
        this.performanceStats.memoryUsage = performance.memory.usedJSHeapSize;
      }
    };
  }

  getPerformanceStats() {
    return { ...this.performanceStats };
  }

  // Cleanup and Disposal
  dispose() {
    // Dispose LOD objects
    this.lodObjects.forEach(lod => {
      lod.levels.forEach(level => {
        if (level.object.geometry) level.object.geometry.dispose();
        if (level.object.material) level.object.material.dispose();
      });
    });
    
    // Dispose instanced meshes
    this.instancedMeshes.forEach(mesh => {
      mesh.geometry.dispose();
      mesh.material.dispose();
    });
    
    // Dispose geometry pool
    if (this.geometryPool) {
      Object.values(this.geometryPool).forEach(pool => {
        pool.forEach(geometry => geometry.dispose());
      });
    }
    
    // Restore original render function
    if (this.originalRender) {
      this.renderer.render = this.originalRender;
    }
  }
}

// Shader optimization utilities
export class ShaderOptimizer {
  static createOptimizedMaterial(type, options = {}) {
    const shaderMaterial = new THREE.ShaderMaterial({
      vertexShader: this.getOptimizedVertexShader(type),
      fragmentShader: this.getOptimizedFragmentShader(type),
      uniforms: this.getUniforms(type, options),
      ...options
    });
    
    return shaderMaterial;
  }

  static getOptimizedVertexShader(type) {
    const shaders = {
      basic: `
        attribute vec3 position;
        attribute vec2 uv;
        uniform mat4 modelViewMatrix;
        uniform mat4 projectionMatrix;
        varying vec2 vUv;
        
        void main() {
          vUv = uv;
          gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
        }
      `,
      earth: `
        attribute vec3 position;
        attribute vec2 uv;
        attribute vec3 normal;
        uniform mat4 modelViewMatrix;
        uniform mat4 projectionMatrix;
        uniform mat3 normalMatrix;
        uniform float time;
        varying vec2 vUv;
        varying vec3 vNormal;
        varying vec3 vPosition;
        
        void main() {
          vUv = uv;
          vNormal = normalMatrix * normal;
          vPosition = position;
          
          // Add subtle vertex displacement for atmosphere
          vec3 displaced = position + normal * sin(time + position.y * 10.0) * 0.01;
          
          gl_Position = projectionMatrix * modelViewMatrix * vec4(displaced, 1.0);
        }
      `
    };
    
    return shaders[type] || shaders.basic;
  }

  static getOptimizedFragmentShader(type) {
    const shaders = {
      basic: `
        uniform sampler2D map;
        varying vec2 vUv;
        
        void main() {
          gl_FragColor = texture2D(map, vUv);
        }
      `,
      earth: `
        uniform sampler2D dayTexture;
        uniform sampler2D nightTexture;
        uniform sampler2D cloudTexture;
        uniform vec3 sunDirection;
        uniform float time;
        varying vec2 vUv;
        varying vec3 vNormal;
        varying vec3 vPosition;
        
        void main() {
          // Calculate lighting
          float intensity = dot(vNormal, sunDirection);
          intensity = smoothstep(-0.5, 0.5, intensity);
          
          // Sample textures
          vec4 dayColor = texture2D(dayTexture, vUv);
          vec4 nightColor = texture2D(nightTexture, vUv);
          vec4 cloudColor = texture2D(cloudTexture, vUv + vec2(time * 0.001, 0.0));
          
          // Mix day and night
          vec4 earthColor = mix(nightColor, dayColor, intensity);
          
          // Add clouds
          earthColor = mix(earthColor, cloudColor, cloudColor.a * 0.5);
          
          // Add atmosphere glow
          float fresnel = 1.0 - dot(vNormal, vec3(0.0, 0.0, 1.0));
          vec3 atmosphere = vec3(0.3, 0.6, 1.0) * pow(fresnel, 2.0);
          
          gl_FragColor = vec4(earthColor.rgb + atmosphere * 0.3, 1.0);
        }
      `
    };
    
    return shaders[type] || shaders.basic;
  }

  static getUniforms(type, options) {
    const uniforms = {
      basic: {
        map: { value: options.map || null }
      },
      earth: {
        dayTexture: { value: options.dayTexture || null },
        nightTexture: { value: options.nightTexture || null },
        cloudTexture: { value: options.cloudTexture || null },
        sunDirection: { value: new THREE.Vector3(1, 0, 0) },
        time: { value: 0 }
      }
    };
    
    return uniforms[type] || uniforms.basic;
  }
}

// WebGL Context Optimization
export class WebGLOptimizer {
  static optimizeContext(renderer) {
    const gl = renderer.getContext();
    
    // Enable extensions for better performance
    const extensions = [
      'OES_vertex_array_object',
      'WEBGL_compressed_texture_s3tc',
      'WEBGL_compressed_texture_pvrtc',
      'WEBGL_compressed_texture_etc1',
      'EXT_texture_filter_anisotropic'
    ];
    
    extensions.forEach(ext => {
      gl.getExtension(ext);
    });
    
    // Optimize GL state
    gl.enable(gl.DEPTH_TEST);
    gl.enable(gl.CULL_FACE);
    gl.cullFace(gl.BACK);
    gl.frontFace(gl.CCW);
    
    return gl;
  }
  
  static createOptimizedRenderer(canvas, options = {}) {
    const renderer = new THREE.WebGLRenderer({
      canvas,
      antialias: false, // Disable for performance, use FXAA instead
      alpha: true,
      premultipliedAlpha: false,
      stencil: false,
      preserveDrawingBuffer: false,
      powerPreference: 'high-performance',
      ...options
    });
    
    // Optimize renderer settings
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    renderer.outputColorSpace = THREE.SRGBColorSpace;
    renderer.toneMapping = THREE.ACESFilmicToneMapping;
    renderer.toneMappingExposure = 1.0;
    
    // Enable optimizations
    renderer.sortObjects = true;
    renderer.shadowMap.enabled = false; // Disable shadows for performance
    
    this.optimizeContext(renderer);
    
    return renderer;
  }
}
