import * as THREE from 'three';
import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
import { generateEarthTexture } from './textureGenerator.js';

// Globe constants
const VLADIVOSTOK_LAT = 43.1332;
const VLADIVOSTOK_LONG = 131.9113;
const GLOBE_RADIUS = 100;

export function createGlobe(container) {
  // Scene setup
  const scene = new THREE.Scene();
  const camera = new THREE.PerspectiveCamera(45, container.clientWidth / container.clientHeight, 0.1, 1000);
  camera.position.z = 300;
  
  const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
  renderer.setSize(container.clientWidth, container.clientHeight);
  container.appendChild(renderer.domElement);
  
  // Controls
  const controls = new OrbitControls(camera, renderer.domElement);
  controls.enableDamping = true;
  controls.dampingFactor = 0.05;
  
  // Create globe
  const globeGeometry = new THREE.SphereGeometry(GLOBE_RADIUS, 64, 64);
  
  // Load textures
  const textureLoader = new THREE.TextureLoader();

  // Try to load earth texture, fallback to generated texture
  let earthTexture;
  try {
    earthTexture = textureLoader.load('assets/earth_texture.jpg');
  } catch (error) {
    // Generate a simple earth texture
    const textureDataUrl = generateEarthTexture();
    earthTexture = textureLoader.load(textureDataUrl);
  }
  
  const globeMaterial = new THREE.MeshPhongMaterial({
    map: earthTexture,
    shininess: 5,
    transparent: true
  });
  
  const globe = new THREE.Mesh(globeGeometry, globeMaterial);
  scene.add(globe);
  
  // Add Vladivostok marker
  const markerGeometry = new THREE.SphereGeometry(2, 16, 16);
  const markerMaterial = new THREE.MeshBasicMaterial({ color: 0xFFD700 });
  const marker = new THREE.Mesh(markerGeometry, markerMaterial);
  
  // Convert lat/long to 3D position
  const phi = (90 - VLADIVOSTOK_LAT) * (Math.PI / 180);
  const theta = (VLADIVOSTOK_LONG + 180) * (Math.PI / 180);
  
  marker.position.x = -GLOBE_RADIUS * Math.sin(phi) * Math.cos(theta);
  marker.position.y = GLOBE_RADIUS * Math.cos(phi);
  marker.position.z = GLOBE_RADIUS * Math.sin(phi) * Math.sin(theta);
  
  globe.add(marker);
  
  // Lighting
  const ambientLight = new THREE.AmbientLight(0x404040);
  scene.add(ambientLight);
  
  const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
  directionalLight.position.set(1, 1, 1);
  scene.add(directionalLight);
  
  // Handle marker click
  const raycaster = new THREE.Raycaster();
  const mouse = new THREE.Vector2();
  
  container.addEventListener('click', (event) => {
    // Calculate mouse position in normalized device coordinates
    mouse.x = (event.clientX / container.clientWidth) * 2 - 1;
    mouse.y = -(event.clientY / container.clientHeight) * 2 + 1;
    
    raycaster.setFromCamera(mouse, camera);
    const intersects = raycaster.intersectObject(marker);
    
    if (intersects.length > 0) {
      zoomToVladivostok();
    }
  });
  
  function zoomToVladivostok() {
    // Animate camera to zoom in on Vladivostok
    const targetPosition = new THREE.Vector3().copy(marker.position).multiplyScalar(1.5);
    
    // Show info popup
    showInfoPopup();
    
    // Animate camera
    const startPosition = camera.position.clone();
    const duration = 1000; // ms
    const startTime = Date.now();
    
    function animateCamera() {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);
      
      // Ease function
      const t = progress < 0.5 ? 2 * progress * progress : -1 + (4 - 2 * progress) * progress;
      
      camera.position.lerpVectors(startPosition, targetPosition, t);
      camera.lookAt(globe.position);
      
      if (progress < 1) {
        requestAnimationFrame(animateCamera);
      }
    }
    
    animateCamera();
  }
  
  function showInfoPopup() {
    const popup = document.createElement('div');
    popup.className = 'info-popup';
    popup.innerHTML = 'Study MBBS at FEFU – 6 years, English-medium, NMC & WHO recognized';
    
    // Position popup near the marker in screen space
    const markerScreenPosition = marker.position.clone();
    markerScreenPosition.project(camera);
    
    popup.style.left = (markerScreenPosition.x * 0.5 + 0.5) * container.clientWidth + 'px';
    popup.style.top = (-markerScreenPosition.y * 0.5 + 0.5) * container.clientHeight + 'px';
    
    container.appendChild(popup);
    
    // Remove popup after a few seconds
    setTimeout(() => {
      popup.classList.add('fade-out');
      setTimeout(() => popup.remove(), 1000);
    }, 5000);
  }
  
  // Animation loop
  function animate() {
    requestAnimationFrame(animate);
    controls.update();
    renderer.render(scene, camera);
  }
  
  animate();
  
  // Handle window resize
  window.addEventListener('resize', () => {
    camera.aspect = container.clientWidth / container.clientHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(container.clientWidth, container.clientHeight);
  });
  
  return {
    globe,
    marker,
    zoomToVladivostok
  };
}