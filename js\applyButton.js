import * as THREE from 'three';

export function createApply<PERSON><PERSON>on(container) {
  // Check WebGL support
  if (!isWebGLSupported()) {
    return createFallbackButton(container);
  }

  try {
    return create3DButton(container);
  } catch (error) {
    console.warn('3D button failed, using fallback:', error);
    return createFallbackButton(container);
  }
}

function create3DButton(container) {
  // Scene setup
  const scene = new THREE.Scene();
  const camera = new THREE.PerspectiveCamera(50, container.clientWidth / container.clientHeight, 0.1, 1000);
  
  const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
  renderer.setSize(container.clientWidth, container.clientHeight);
  renderer.setClearColor(0x000000, 0);
  container.appendChild(renderer.domElement);

  // Create button geometry
  const buttonGroup = new THREE.Group();
  scene.add(buttonGroup);

  // Main button
  const buttonGeometry = new THREE.CylinderGeometry(2, 2, 0.5, 32);
  const buttonMaterial = new THREE.MeshPhongMaterial({
    color: 0x0056b3,
    shininess: 100,
    transparent: true,
    opacity: 0.9
  });
  const button = new THREE.Mesh(buttonGeometry, buttonMaterial);
  buttonGroup.add(button);

  // Button ring
  const ringGeometry = new THREE.TorusGeometry(2.2, 0.1, 8, 32);
  const ringMaterial = new THREE.MeshPhongMaterial({
    color: 0xFFD700,
    shininess: 100
  });
  const ring = new THREE.Mesh(ringGeometry, ringMaterial);
  buttonGroup.add(ring);

  // Floating particles
  const particles = [];
  const particleGeometry = new THREE.SphereGeometry(0.05, 8, 8);
  const particleMaterial = new THREE.MeshBasicMaterial({
    color: 0xFFD700,
    transparent: true,
    opacity: 0.8
  });

  for (let i = 0; i < 20; i++) {
    const particle = new THREE.Mesh(particleGeometry, particleMaterial);
    const angle = (i / 20) * Math.PI * 2;
    const radius = 3 + Math.random() * 2;
    
    particle.position.x = Math.cos(angle) * radius;
    particle.position.z = Math.sin(angle) * radius;
    particle.position.y = (Math.random() - 0.5) * 2;
    
    particle.userData = {
      originalY: particle.position.y,
      speed: 0.01 + Math.random() * 0.02,
      amplitude: 0.5 + Math.random() * 0.5
    };
    
    buttonGroup.add(particle);
    particles.push(particle);
  }

  // Lighting
  const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
  scene.add(ambientLight);

  const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
  directionalLight.position.set(5, 5, 5);
  scene.add(directionalLight);

  const pointLight = new THREE.PointLight(0xFFD700, 0.5, 10);
  pointLight.position.set(0, 3, 0);
  scene.add(pointLight);

  // Camera positioning
  camera.position.set(0, 2, 8);
  camera.lookAt(0, 0, 0);

  // Animation variables
  let time = 0;
  let isHovered = false;
  let isPressed = false;

  // Mouse interaction
  const mouse = new THREE.Vector2();
  const raycaster = new THREE.Raycaster();

  container.addEventListener('mousemove', (event) => {
    const rect = container.getBoundingClientRect();
    mouse.x = ((event.clientX - rect.left) / container.clientWidth) * 2 - 1;
    mouse.y = -((event.clientY - rect.top) / container.clientHeight) * 2 + 1;

    raycaster.setFromCamera(mouse, camera);
    const intersects = raycaster.intersectObject(button);
    
    if (intersects.length > 0 && !isHovered) {
      isHovered = true;
      onButtonHover();
    } else if (intersects.length === 0 && isHovered) {
      isHovered = false;
      onButtonLeave();
    }
  });

  container.addEventListener('click', (event) => {
    const rect = container.getBoundingClientRect();
    mouse.x = ((event.clientX - rect.left) / container.clientWidth) * 2 - 1;
    mouse.y = -((event.clientY - rect.top) / container.clientHeight) * 2 + 1;

    raycaster.setFromCamera(mouse, camera);
    const intersects = raycaster.intersectObject(button);
    
    if (intersects.length > 0) {
      onButtonClick();
    }
  });

  function onButtonHover() {
    // Animate button on hover
    gsap.to(button.scale, { duration: 0.3, x: 1.1, y: 1.1, z: 1.1 });
    gsap.to(ring.rotation, { duration: 2, z: Math.PI * 2, repeat: -1, ease: "none" });
    gsap.to(buttonMaterial, { duration: 0.3, opacity: 1 });
    
    // Animate particles
    particles.forEach((particle, i) => {
      gsap.to(particle.position, {
        duration: 0.5,
        y: particle.userData.originalY + particle.userData.amplitude,
        delay: i * 0.02
      });
    });
  }

  function onButtonLeave() {
    // Reset button
    gsap.to(button.scale, { duration: 0.3, x: 1, y: 1, z: 1 });
    gsap.to(ring.rotation, { duration: 0.3, z: 0 });
    gsap.to(buttonMaterial, { duration: 0.3, opacity: 0.9 });
    
    // Reset particles
    particles.forEach((particle, i) => {
      gsap.to(particle.position, {
        duration: 0.5,
        y: particle.userData.originalY,
        delay: i * 0.02
      });
    });
  }

  function onButtonClick() {
    // Button press animation
    gsap.to(button.scale, { duration: 0.1, x: 0.95, y: 0.8, z: 0.95 });
    gsap.to(button.scale, { duration: 0.2, x: 1.1, y: 1.1, z: 1.1, delay: 0.1 });
    
    // Create explosion effect
    createExplosionEffect();
    
    // Trigger application action
    handleApplyAction();
  }

  function createExplosionEffect() {
    const explosionParticles = [];
    const explosionGeometry = new THREE.SphereGeometry(0.1, 8, 8);
    const explosionMaterial = new THREE.MeshBasicMaterial({
      color: 0xFFD700,
      transparent: true
    });

    for (let i = 0; i < 15; i++) {
      const particle = new THREE.Mesh(explosionGeometry, explosionMaterial);
      particle.position.copy(button.position);
      
      const direction = new THREE.Vector3(
        (Math.random() - 0.5) * 2,
        (Math.random() - 0.5) * 2,
        (Math.random() - 0.5) * 2
      ).normalize();
      
      particle.userData.velocity = direction.multiplyScalar(0.2);
      
      buttonGroup.add(particle);
      explosionParticles.push(particle);
    }

    // Animate explosion particles
    explosionParticles.forEach(particle => {
      gsap.to(particle.position, {
        duration: 1,
        x: particle.position.x + particle.userData.velocity.x * 10,
        y: particle.position.y + particle.userData.velocity.y * 10,
        z: particle.position.z + particle.userData.velocity.z * 10
      });
      
      gsap.to(particle.material, {
        duration: 1,
        opacity: 0,
        onComplete: () => {
          buttonGroup.remove(particle);
        }
      });
    });
  }

  // Animation loop
  function animate() {
    requestAnimationFrame(animate);
    
    time += 0.01;
    
    // Rotate button group
    buttonGroup.rotation.y += 0.005;
    
    // Animate particles
    particles.forEach(particle => {
      particle.position.y = particle.userData.originalY + 
        Math.sin(time * particle.userData.speed * 10) * particle.userData.amplitude * 0.3;
      particle.rotation.x += particle.userData.speed;
      particle.rotation.y += particle.userData.speed;
    });
    
    // Pulse ring
    ring.scale.setScalar(1 + Math.sin(time * 3) * 0.1);
    
    renderer.render(scene, camera);
  }

  animate();

  // Handle resize
  const handleResize = () => {
    camera.aspect = container.clientWidth / container.clientHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(container.clientWidth, container.clientHeight);
  };

  window.addEventListener('resize', handleResize);

  // Add text overlay
  createButtonText(container);

  return {
    scene,
    camera,
    renderer,
    buttonGroup,
    handleResize,
    destroy: () => {
      window.removeEventListener('resize', handleResize);
      renderer.dispose();
      container.removeChild(renderer.domElement);
    }
  };
}

function createButtonText(container) {
  const textOverlay = document.createElement('div');
  textOverlay.className = 'button-text-overlay';
  textOverlay.innerHTML = `
    <h3>Ready to Start Your Medical Journey?</h3>
    <p>Apply now for MBBS at FEFU</p>
    <div class="button-cta">
      <span class="cta-text">APPLY NOW</span>
      <i class="fas fa-arrow-right"></i>
    </div>
  `;
  
  container.appendChild(textOverlay);
}

function createFallbackButton(container) {
  container.innerHTML = `
    <div class="fallback-apply-button">
      <div class="apply-button-content">
        <h3>Ready to Start Your Medical Journey?</h3>
        <p>Apply now for MBBS at FEFU</p>
        <button class="apply-btn-fallback" onclick="handleApplyAction()">
          <span>APPLY NOW</span>
          <i class="fas fa-arrow-right"></i>
        </button>
      </div>
    </div>
  `;

  return {
    type: 'fallback',
    container
  };
}

function handleApplyAction() {
  // Track application click
  if (typeof gtag !== 'undefined') {
    gtag('event', 'apply_button_click', {
      'event_category': 'conversion',
      'event_label': 'main_apply_button'
    });
  }

  // Scroll to application form
  const applicationForm = document.querySelector('.application-form');
  if (applicationForm) {
    applicationForm.scrollIntoView({ 
      behavior: 'smooth',
      block: 'center'
    });
    
    // Focus on first input
    setTimeout(() => {
      const firstInput = applicationForm.querySelector('input');
      if (firstInput) {
        firstInput.focus();
      }
    }, 1000);
  }

  // Show success message
  showApplyMessage();
}

function showApplyMessage() {
  const message = document.createElement('div');
  message.className = 'apply-message';
  message.innerHTML = `
    <div class="message-content">
      <i class="fas fa-check-circle"></i>
      <p>Great choice! Please fill out the enquiry form below.</p>
    </div>
  `;
  
  document.body.appendChild(message);
  
  setTimeout(() => {
    message.classList.add('show');
  }, 100);
  
  setTimeout(() => {
    message.classList.remove('show');
    setTimeout(() => message.remove(), 300);
  }, 3000);
}

function isWebGLSupported() {
  try {
    const canvas = document.createElement('canvas');
    return !!(window.WebGLRenderingContext && canvas.getContext('webgl'));
  } catch (e) {
    return false;
  }
}

// Make handleApplyAction globally available
window.handleApplyAction = handleApplyAction;
