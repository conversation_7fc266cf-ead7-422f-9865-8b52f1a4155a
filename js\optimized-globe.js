import * as THREE from 'three';
import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
import { generateEarthTexture } from './textureGenerator.js';

// Globe constants
const VLADIVOSTOK_LAT = 43.1332;
const VLADIVOSTOK_LONG = 131.9113;
const GLOBE_RADIUS = 100;

// Object pool for performance optimization
class ObjectPool {
  constructor(createFn, resetFn, initialSize = 10) {
    this.createFn = createFn;
    this.resetFn = resetFn;
    this.pool = [];
    this.active = [];
    
    // Pre-populate pool
    for (let i = 0; i < initialSize; i++) {
      this.pool.push(this.createFn());
    }
  }
  
  get() {
    let obj = this.pool.pop();
    if (!obj) {
      obj = this.createFn();
    }
    this.active.push(obj);
    return obj;
  }
  
  release(obj) {
    const index = this.active.indexOf(obj);
    if (index > -1) {
      this.active.splice(index, 1);
      this.resetFn(obj);
      this.pool.push(obj);
    }
  }
  
  releaseAll() {
    this.active.forEach(obj => {
      this.resetFn(obj);
      this.pool.push(obj);
    });
    this.active.length = 0;
  }
}

export function createOptimizedGlobe(container, deviceCapabilities) {
  // Check if we should use 3D
  if (!deviceCapabilities.shouldUse3D()) {
    return createFallbackGlobe(container);
  }

  // Performance-optimized scene setup
  const scene = new THREE.Scene();
  const camera = new THREE.PerspectiveCamera(
    45, 
    container.clientWidth / container.clientHeight, 
    0.1, 
    1000
  );
  camera.position.z = 300;
  
  // Optimized renderer settings
  const renderer = new THREE.WebGLRenderer({ 
    antialias: !deviceCapabilities.capabilities.mobile,
    alpha: true,
    powerPreference: deviceCapabilities.capabilities.mobile ? 'low-power' : 'high-performance',
    stencil: false,
    depth: true
  });
  
  renderer.setSize(container.clientWidth, container.clientHeight);
  renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2)); // Limit pixel ratio for performance
  renderer.outputColorSpace = THREE.SRGBColorSpace;
  container.appendChild(renderer.domElement);
  
  // Optimized controls
  const controls = new OrbitControls(camera, renderer.domElement);
  controls.enableDamping = true;
  controls.dampingFactor = 0.05;
  controls.enableZoom = !deviceCapabilities.capabilities.mobile; // Disable zoom on mobile
  controls.maxDistance = 500;
  controls.minDistance = 200;
  
  // Geometry optimization based on device capabilities
  const globeDetail = deviceCapabilities.capabilities.mobile ? 32 : 64;
  const globeGeometry = new THREE.SphereGeometry(GLOBE_RADIUS, globeDetail, globeDetail);
  
  // Texture management with caching
  const textureCache = new Map();
  const textureLoader = new THREE.TextureLoader();
  
  async function loadTexture(url, fallbackGenerator) {
    if (textureCache.has(url)) {
      return textureCache.get(url);
    }
    
    try {
      const texture = await new Promise((resolve, reject) => {
        textureLoader.load(url, resolve, undefined, reject);
      });
      
      // Optimize texture
      texture.generateMipmaps = true;
      texture.minFilter = THREE.LinearMipmapLinearFilter;
      texture.magFilter = THREE.LinearFilter;
      texture.wrapS = THREE.RepeatWrapping;
      texture.wrapT = THREE.ClampToEdgeWrapping;
      
      textureCache.set(url, texture);
      return texture;
    } catch (error) {
      console.warn('Failed to load texture, using fallback:', error);
      const fallbackDataUrl = fallbackGenerator();
      return loadTexture(fallbackDataUrl, () => fallbackDataUrl);
    }
  }
  
  // Load earth texture with fallback
  let earthTexture;
  const initTexture = async () => {
    earthTexture = await loadTexture('assets/earth_texture.jpg', generateEarthTexture);
    globeMaterial.map = earthTexture;
    globeMaterial.needsUpdate = true;
  };
  
  // Optimized materials
  const globeMaterial = new THREE.MeshPhongMaterial({
    shininess: 5,
    transparent: false, // Disable transparency for performance
    side: THREE.FrontSide
  });
  
  const globe = new THREE.Mesh(globeGeometry, globeMaterial);
  scene.add(globe);
  
  // Optimized marker
  const markerDetail = deviceCapabilities.capabilities.mobile ? 8 : 16;
  const markerGeometry = new THREE.SphereGeometry(2, markerDetail, markerDetail);
  const markerMaterial = new THREE.MeshBasicMaterial({ 
    color: 0xFFD700,
    transparent: true,
    opacity: 0.9
  });
  const marker = new THREE.Mesh(markerGeometry, markerMaterial);
  
  // Calculate marker position
  const phi = (90 - VLADIVOSTOK_LAT) * (Math.PI / 180);
  const theta = (VLADIVOSTOK_LONG + 180) * (Math.PI / 180);
  
  marker.position.x = -GLOBE_RADIUS * Math.sin(phi) * Math.cos(theta);
  marker.position.y = GLOBE_RADIUS * Math.cos(phi);
  marker.position.z = GLOBE_RADIUS * Math.sin(phi) * Math.sin(theta);
  
  globe.add(marker);
  
  // Optimized lighting
  const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
  scene.add(ambientLight);
  
  const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
  directionalLight.position.set(1, 1, 1);
  directionalLight.castShadow = false; // Disable shadows for performance
  scene.add(directionalLight);
  
  // Interaction handling with debouncing
  const raycaster = new THREE.Raycaster();
  const mouse = new THREE.Vector2();
  let isInteracting = false;
  let interactionTimeout;
  
  const handleInteraction = (event) => {
    if (isInteracting) return;
    
    const rect = container.getBoundingClientRect();
    mouse.x = ((event.clientX - rect.left) / container.clientWidth) * 2 - 1;
    mouse.y = -((event.clientY - rect.top) / container.clientHeight) * 2 + 1;
    
    raycaster.setFromCamera(mouse, camera);
    const intersects = raycaster.intersectObject(marker);
    
    if (intersects.length > 0) {
      isInteracting = true;
      zoomToVladivostok();
      
      // Prevent rapid interactions
      clearTimeout(interactionTimeout);
      interactionTimeout = setTimeout(() => {
        isInteracting = false;
      }, 2000);
    }
  };
  
  container.addEventListener('click', handleInteraction);
  container.addEventListener('touchend', handleInteraction);
  
  // Optimized zoom animation
  function zoomToVladivostok() {
    const targetPosition = new THREE.Vector3().copy(marker.position).multiplyScalar(1.5);
    const startPosition = camera.position.clone();
    const duration = 1000;
    const startTime = Date.now();
    
    showInfoPopup();
    
    function animateCamera() {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);
      
      // Smooth easing function
      const t = progress < 0.5 ? 2 * progress * progress : -1 + (4 - 2 * progress) * progress;
      
      camera.position.lerpVectors(startPosition, targetPosition, t);
      camera.lookAt(globe.position);
      
      if (progress < 1) {
        requestAnimationFrame(animateCamera);
      }
    }
    
    animateCamera();
  }
  
  // Object pool for popup elements
  const popupPool = new ObjectPool(
    () => {
      const popup = document.createElement('div');
      popup.className = 'info-popup';
      return popup;
    },
    (popup) => {
      popup.remove();
      popup.className = 'info-popup';
      popup.innerHTML = '';
    },
    3
  );
  
  function showInfoPopup() {
    const popup = popupPool.get();
    popup.innerHTML = 'Study MBBS at FEFU – 6 years, English-medium, NMC & WHO recognized';
    popup.setAttribute('role', 'alert');
    popup.setAttribute('aria-live', 'polite');
    
    // Position popup
    const markerScreenPosition = marker.position.clone();
    markerScreenPosition.project(camera);
    
    const x = (markerScreenPosition.x * 0.5 + 0.5) * container.clientWidth;
    const y = (-markerScreenPosition.y * 0.5 + 0.5) * container.clientHeight;
    
    popup.style.left = `${Math.max(10, Math.min(x - 150, container.clientWidth - 310))}px`;
    popup.style.top = `${Math.max(10, Math.min(y - 30, container.clientHeight - 70))}px`;
    
    container.appendChild(popup);
    
    // Auto-remove popup
    setTimeout(() => {
      popup.classList.add('fade-out');
      setTimeout(() => popupPool.release(popup), 1000);
    }, 5000);
  }
  
  // Performance monitoring
  let frameCount = 0;
  let lastFPSCheck = Date.now();
  let currentFPS = 60;
  let isLowPerformance = false;
  
  // Optimized animation loop
  let animationId;
  let isVisible = true;
  
  function animate() {
    animationId = requestAnimationFrame(animate);
    
    // Skip rendering if not visible
    if (!isVisible) return;
    
    // Performance monitoring
    frameCount++;
    const now = Date.now();
    if (now - lastFPSCheck >= 1000) {
      currentFPS = frameCount;
      frameCount = 0;
      lastFPSCheck = now;
      
      // Adjust quality based on performance
      if (currentFPS < 30 && !isLowPerformance) {
        isLowPerformance = true;
        reduceQuality();
      } else if (currentFPS > 50 && isLowPerformance) {
        isLowPerformance = false;
        restoreQuality();
      }
    }
    
    // Update controls
    controls.update();
    
    // Subtle globe rotation
    globe.rotation.y += 0.001;
    
    // Marker pulse animation
    const time = Date.now() * 0.001;
    marker.scale.setScalar(1 + Math.sin(time * 2) * 0.1);
    
    // Render
    renderer.render(scene, camera);
  }
  
  function reduceQuality() {
    renderer.setPixelRatio(1);
    globeMaterial.shininess = 0;
    marker.material.transparent = false;
  }
  
  function restoreQuality() {
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    globeMaterial.shininess = 5;
    marker.material.transparent = true;
  }
  
  // Visibility API for performance
  document.addEventListener('visibilitychange', () => {
    isVisible = !document.hidden;
  });
  
  // Intersection observer for container visibility
  const visibilityObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      isVisible = entry.isIntersecting;
    });
  }, { threshold: 0.1 });
  
  visibilityObserver.observe(container);
  
  // Start animation and texture loading
  animate();
  initTexture();
  
  // Optimized resize handler
  let resizeTimeout;
  const handleResize = () => {
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(() => {
      const width = container.clientWidth;
      const height = container.clientHeight;
      
      camera.aspect = width / height;
      camera.updateProjectionMatrix();
      renderer.setSize(width, height);
    }, 150);
  };
  
  window.addEventListener('resize', handleResize);
  
  // Cleanup function
  const destroy = () => {
    cancelAnimationFrame(animationId);
    window.removeEventListener('resize', handleResize);
    visibilityObserver.disconnect();
    
    // Dispose of Three.js objects
    globeGeometry.dispose();
    markerGeometry.dispose();
    globeMaterial.dispose();
    markerMaterial.dispose();
    
    // Dispose of textures
    textureCache.forEach(texture => texture.dispose());
    textureCache.clear();
    
    // Dispose of renderer
    renderer.dispose();
    
    // Release object pools
    popupPool.releaseAll();
    
    // Remove canvas
    if (container.contains(renderer.domElement)) {
      container.removeChild(renderer.domElement);
    }
  };
  
  return {
    globe,
    marker,
    scene,
    camera,
    renderer,
    zoomToVladivostok,
    handleResize,
    destroy,
    optimize: () => {
      // Force garbage collection of unused objects
      popupPool.releaseAll();
      
      // Reduce texture quality if needed
      if (earthTexture) {
        earthTexture.minFilter = THREE.LinearFilter;
        earthTexture.generateMipmaps = false;
      }
    },
    reduceQuality,
    restoreQuality,
    getCurrentFPS: () => currentFPS
  };
}

function createFallbackGlobe(container) {
  container.innerHTML = `
    <div class="fallback-globe">
      <div class="globe-placeholder">
        <div class="earth-icon">🌍</div>
        <h3>Interactive Globe</h3>
        <p>Click to explore FEFU's location in Vladivostok, Russia</p>
        <button class="explore-btn" onclick="showVladivostokInfo()">
          Explore Location
        </button>
      </div>
    </div>
  `;
  
  // Add fallback interaction
  window.showVladivostokInfo = () => {
    const info = document.createElement('div');
    info.className = 'location-info';
    info.innerHTML = `
      <h4>Far Eastern Federal University</h4>
      <p>Located in Vladivostok, Russia</p>
      <p>Study MBBS – 6 years, English-medium, NMC & WHO recognized</p>
    `;
    
    container.appendChild(info);
    
    setTimeout(() => {
      info.classList.add('fade-out');
      setTimeout(() => info.remove(), 1000);
    }, 5000);
  };
  
  return {
    type: 'fallback',
    container,
    destroy: () => {
      delete window.showVladivostokInfo;
    }
  };
}
