# MBBS Vostrix - Technical Documentation

## 🏗️ Architecture Overview

The MBBS Vostrix website is built using a modular architecture with the following key components:

### Core Technologies
- **Three.js**: 3D graphics and WebGL rendering
- **GSAP**: Advanced animations and transitions
- **Vanilla JavaScript**: ES6+ modules for component management
- **CSS Grid/Flexbox**: Responsive layout system
- **Intersection Observer API**: Scroll-triggered animations

## 📦 Component Breakdown

### 1. Main Application (`js/main.js`)
**Purpose**: Central orchestrator for all components
**Key Features**:
- Component lifecycle management
- Event handling and coordination
- Responsive behavior management
- Error handling and fallbacks

```javascript
class MBBSVostrixApp {
  constructor() {
    this.components = {};
    this.isLoaded = false;
    this.isMobile = window.innerWidth <= 768;
  }
}
```

### 2. Interactive Globe (`js/globe.js`)
**Purpose**: 3D Earth with Vladivostok marker
**Technical Details**:
- Three.js SphereGeometry with earth texture
- OrbitControls for user interaction
- Raycasting for click detection
- Smooth camera animations

**Key Features**:
- Clickable Vladivostok hotspot
- Zoom-in animation on click
- Info popup display
- Responsive canvas sizing

### 3. Info Cards (`js/infoCards.js`)
**Purpose**: 3D flip cards with program information
**Technical Details**:
- CSS 3D transforms for flip effect
- Intersection Observer for animations
- Touch support for mobile devices
- Analytics tracking integration

**Data Structure**:
```javascript
const cardData = [
  {
    id: 'tuition',
    icon: '💰',
    title: 'Tuition Fees',
    details: {
      title: 'Annual Tuition',
      content: ['440,000 - 495,000 RUB/year', '≈ ₹3.8 - 4.95 Lakhs/year']
    }
  }
];
```

### 4. Campus Gallery (`js/campusGallery.js`)
**Purpose**: 3D rotating image carousel
**Technical Details**:
- Three.js PlaneGeometry for image panels
- Circular arrangement with radius positioning
- Texture loading with fallback handling
- Mouse/touch controls for rotation

**Features**:
- Auto-rotation with pause on interaction
- Click-to-view modal functionality
- Navigation dots for direct access
- Fallback 2D grid for unsupported devices

### 5. Advantages Section (`js/advantages.js`)
**Purpose**: Interactive advantage items with 3D effects
**Technical Details**:
- CSS 3D transforms and animations
- Particle effects on hover
- Modal system for detailed information
- Staggered animation entrance

### 6. Apply Button (`js/applyButton.js`)
**Purpose**: 3D animated call-to-action button
**Technical Details**:
- Three.js CylinderGeometry for button shape
- Particle system for visual effects
- GSAP animations for interactions
- Explosion effect on click

## 🎨 Styling Architecture

### CSS Organization
1. **styles.css**: Base styles and layout
2. **animations.css**: Animation definitions and keyframes
3. **additional-styles.css**: Component-specific styles

### CSS Custom Properties
```css
:root {
  --primary-color: #0056b3;
  --secondary-color: #ffd700;
  --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.15);
}
```

### Responsive Breakpoints
- **Mobile**: < 768px
- **Tablet**: 768px - 1199px
- **Desktop**: 1200px+

## 🔄 Animation System

### Types of Animations
1. **CSS Animations**: Simple transitions and keyframes
2. **GSAP Animations**: Complex 3D transformations
3. **Three.js Animations**: 3D object movements
4. **Scroll Animations**: Intersection Observer triggered

### Performance Considerations
- `transform` and `opacity` properties for GPU acceleration
- `will-change` property for optimization hints
- Animation frame limiting for smooth performance
- Reduced motion support for accessibility

## 📱 Responsive Strategy

### Mobile-First Approach
1. **Base styles**: Mobile layout
2. **Progressive enhancement**: Tablet and desktop features
3. **Feature detection**: WebGL and touch capabilities
4. **Graceful degradation**: Fallbacks for unsupported features

### Touch Interactions
- Touch events for 3D component manipulation
- Swipe gestures for gallery navigation
- Tap interactions for card flipping
- Haptic feedback where supported

## 🛡️ Error Handling & Fallbacks

### WebGL Detection
```javascript
function isWebGLSupported() {
  try {
    const canvas = document.createElement('canvas');
    return !!(window.WebGLRenderingContext && canvas.getContext('webgl'));
  } catch (e) {
    return false;
  }
}
```

### Fallback Strategies
1. **3D to 2D**: Static images replace 3D scenes
2. **Animation fallbacks**: CSS animations replace GSAP
3. **Texture fallbacks**: Generated textures replace external images
4. **Progressive loading**: Essential content loads first

## 🔧 Performance Optimization

### Loading Strategy
1. **Critical CSS**: Inline essential styles
2. **Lazy loading**: Images and 3D components on demand
3. **Code splitting**: Modular JavaScript loading
4. **Resource hints**: Preload, prefetch, and preconnect

### 3D Optimization
- **Low-poly models**: Reduced vertex count
- **Texture compression**: Optimized image formats
- **LOD system**: Level of detail based on distance
- **Frustum culling**: Only render visible objects

### Memory Management
- **Dispose patterns**: Clean up Three.js resources
- **Event listener cleanup**: Remove unused listeners
- **Texture pooling**: Reuse texture resources
- **Component lifecycle**: Proper initialization and destruction

## 📊 Analytics Integration

### Event Tracking
```javascript
function trackCardInteraction(cardId) {
  if (typeof gtag !== 'undefined') {
    gtag('event', 'card_interaction', {
      'card_id': cardId,
      'event_category': 'engagement'
    });
  }
}
```

### Tracked Events
- Card interactions
- Globe clicks
- Apply button clicks
- Form submissions
- Error occurrences

## 🔒 Security Considerations

### Content Security Policy
- Restrict script sources
- Prevent XSS attacks
- Secure external resource loading

### Data Handling
- Client-side form validation
- Secure form submission
- No sensitive data storage

## 🧪 Testing Strategy

### Browser Testing
- Cross-browser compatibility
- WebGL feature detection
- Touch device testing
- Performance profiling

### Accessibility Testing
- Screen reader compatibility
- Keyboard navigation
- Color contrast validation
- Reduced motion support

## 🚀 Deployment Considerations

### Build Process
1. **Asset optimization**: Image compression and minification
2. **Code bundling**: JavaScript module concatenation
3. **CSS optimization**: Minification and purging
4. **Cache strategies**: Proper cache headers

### CDN Integration
- Three.js from CDN for faster loading
- Font loading optimization
- Image delivery optimization

## 📈 Future Scalability

### Modular Architecture Benefits
- Easy component addition/removal
- Independent component updates
- Reusable component library
- Maintainable codebase

### Potential Enhancements
- WebAssembly for complex calculations
- Web Workers for background processing
- Service Workers for offline functionality
- WebXR for VR/AR experiences

## 🔍 Debugging & Development

### Development Tools
- Browser DevTools for debugging
- Three.js Inspector for 3D debugging
- Performance profiling tools
- Accessibility auditing tools

### Common Issues & Solutions
1. **WebGL context loss**: Implement context restoration
2. **Memory leaks**: Proper resource disposal
3. **Performance issues**: Optimize render loops
4. **Mobile compatibility**: Touch event handling

---

This technical documentation provides a comprehensive overview of the MBBS Vostrix website implementation. For specific implementation details, refer to the individual component files and their inline documentation.
