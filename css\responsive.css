/* ===== RESPONSIVE DESIGN ===== */

/* Large Desktop (1400px+) */
@media (min-width: 1400px) {
  .hero h1 {
    font-size: 4rem;
  }
  
  .hero h2 {
    font-size: 2.5rem;
  }
  
  .container {
    max-width: 1320px;
  }
}

/* Desktop (1200px - 1399px) */
@media (max-width: 1399px) {
  .hero h1 {
    font-size: var(--font-size-5xl);
  }
  
  .hero h2 {
    font-size: var(--font-size-3xl);
  }
  
  .info-cards-container {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }
}

/* Tablet Landscape (992px - 1199px) */
@media (max-width: 1199px) {
  header {
    padding: var(--spacing-md) 3%;
  }
  
  .hero {
    padding: var(--header-height) 3% 0;
  }
  
  .hero h1 {
    font-size: 2.8rem;
  }
  
  .hero h2 {
    font-size: 2rem;
  }
  
  .hero-highlights {
    gap: var(--spacing-md);
  }
  
  .highlight-item {
    padding: var(--spacing-xs) var(--spacing-md);
  }
  
  .section-padding {
    padding: var(--spacing-3xl) 3% var(--spacing-2xl);
  }
}

/* Tablet Portrait (768px - 991px) */
@media (max-width: 991px) {
  .hero {
    flex-direction: column;
    text-align: center;
    min-height: auto;
    padding: calc(var(--header-height) + var(--spacing-xl)) 3% var(--spacing-xl);
  }
  
  .hero-content {
    width: 100%;
    margin-bottom: var(--spacing-xl);
  }
  
  .globe-container {
    width: 100%;
    height: 400px;
    max-width: 600px;
  }
  
  .hero-highlights {
    justify-content: center;
  }
  
  .hero-cta {
    justify-content: center;
  }
  
  .info-cards-container {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
  }
  
  .advantages-container {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }
}

/* Mobile Landscape & Large Mobile (481px - 767px) */
@media (max-width: 767px) {
  header {
    flex-direction: column;
    gap: var(--spacing-md);
    padding: var(--spacing-md) 3%;
    height: auto;
    min-height: var(--header-height);
  }
  
  .nav-toggle {
    display: block;
    position: absolute;
    top: var(--spacing-md);
    right: 3%;
  }
  
  nav {
    width: 100%;
    order: 2;
  }
  
  nav ul {
    flex-direction: column;
    gap: var(--spacing-md);
    text-align: center;
    max-height: 0;
    overflow: hidden;
    transition: var(--transition-smooth);
  }
  
  nav.nav-open ul {
    max-height: 300px;
    padding: var(--spacing-md) 0;
  }
  
  .hero {
    padding-top: calc(var(--header-height) + var(--spacing-2xl));
  }
  
  .hero h1 {
    font-size: 2.5rem;
    line-height: 1.1;
  }
  
  .hero h2 {
    font-size: 1.8rem;
  }
  
  .hero p {
    font-size: var(--font-size-base);
  }
  
  .hero-highlights {
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
  }
  
  .highlight-item {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }
  
  .hero-cta {
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
  }
  
  .cta-button {
    width: 100%;
    max-width: 280px;
    justify-content: center;
  }
  
  .globe-container {
    height: 350px;
  }
  
  .section-padding {
    padding: var(--spacing-2xl) 3% var(--spacing-xl);
  }
  
  .section-title {
    font-size: var(--font-size-3xl);
  }
  
  .info-cards-container {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
  
  .info-card {
    height: 280px;
    max-width: 400px;
    margin: 0 auto;
  }
  
  .advantages-container {
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
  }
  
  .campus-gallery {
    height: 300px;
    margin: var(--spacing-xl) auto;
  }
  
  .application-form {
    margin: var(--spacing-xl) var(--spacing-md);
    padding: var(--spacing-xl);
  }
  
  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
    gap: var(--spacing-xl);
  }
}

/* Mobile Portrait (320px - 480px) */
@media (max-width: 480px) {
  :root {
    --font-size-5xl: 2rem;
    --font-size-4xl: 1.8rem;
    --font-size-3xl: 1.5rem;
    --font-size-2xl: 1.3rem;
    --spacing-xl: 1.5rem;
    --spacing-2xl: 2rem;
    --spacing-3xl: 2.5rem;
  }
  
  header {
    padding: var(--spacing-sm) var(--spacing-md);
  }
  
  .logo {
    font-size: var(--font-size-xl);
  }
  
  .nav-toggle {
    top: var(--spacing-sm);
    right: var(--spacing-md);
  }
  
  .hero {
    padding: calc(var(--header-height) + var(--spacing-xl)) var(--spacing-md) var(--spacing-xl);
  }
  
  .hero h1 {
    font-size: var(--font-size-5xl);
    margin-bottom: var(--spacing-sm);
  }
  
  .hero h2 {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--spacing-sm);
  }
  
  .hero p {
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-md);
  }
  
  .hero-highlights {
    gap: var(--spacing-sm);
  }
  
  .highlight-item {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
  }
  
  .highlight-item i {
    font-size: var(--font-size-base);
  }
  
  .cta-button {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-base);
  }
  
  .globe-container {
    height: 280px;
  }
  
  .globe-info {
    font-size: var(--font-size-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
  }
  
  .section-padding {
    padding: var(--spacing-xl) var(--spacing-md);
  }
  
  .section-title {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-sm);
  }
  
  .section-description {
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-lg);
  }
  
  .info-card {
    height: 250px;
  }
  
  .info-card-front,
  .info-card-back {
    padding: var(--spacing-md);
  }
  
  .info-card-front h3 {
    font-size: var(--font-size-lg);
  }
  
  .info-card-front .card-icon {
    font-size: var(--font-size-3xl);
  }
  
  .advantage-item {
    padding: var(--spacing-md);
  }
  
  .advantage-icon {
    width: 60px;
    height: 60px;
    font-size: var(--font-size-xl);
  }
  
  .advantage-item h3 {
    font-size: var(--font-size-lg);
  }
  
  .advantage-item p {
    font-size: var(--font-size-sm);
  }
  
  .application-form {
    padding: var(--spacing-md);
    margin: var(--spacing-md);
  }
  
  .form-group {
    margin-bottom: var(--spacing-md);
  }
  
  .form-group input,
  .form-group textarea {
    padding: var(--spacing-sm);
    font-size: var(--font-size-sm);
  }
  
  .submit-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-base);
  }
}

/* Extra Small Mobile (< 320px) */
@media (max-width: 319px) {
  .hero h1 {
    font-size: 1.8rem;
  }
  
  .hero h2 {
    font-size: 1.3rem;
  }
  
  .highlight-item {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-xs);
  }
  
  .cta-button {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
  }
  
  .globe-container {
    height: 240px;
  }
  
  .info-card {
    height: 220px;
  }
}

/* ===== TOUCH DEVICE OPTIMIZATIONS ===== */
@media (hover: none) and (pointer: coarse) {
  .cta-button:hover,
  .highlight-item:hover,
  .info-card:hover {
    transform: none;
  }
  
  .cta-button:active {
    transform: scale(0.98);
  }
  
  .highlight-item:active {
    background: rgba(255, 255, 255, 0.3);
  }
  
  /* Increase touch targets */
  .nav-toggle {
    min-width: 44px;
    min-height: 44px;
  }
  
  nav ul li a {
    padding: var(--spacing-md) var(--spacing-sm);
  }
  
  .gallery-dot {
    min-width: 44px;
    min-height: 44px;
  }
}

/* ===== LANDSCAPE ORIENTATION ===== */
@media (orientation: landscape) and (max-height: 500px) {
  .hero {
    min-height: auto;
    padding: calc(var(--header-height) + var(--spacing-md)) 3% var(--spacing-md);
  }
  
  .hero h1 {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--spacing-xs);
  }
  
  .hero h2 {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-xs);
  }
  
  .hero p {
    margin-bottom: var(--spacing-sm);
  }
  
  .hero-highlights {
    margin: var(--spacing-sm) 0;
  }
  
  .globe-container {
    height: 250px;
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  header {
    position: static;
    box-shadow: none;
    background: var(--white);
  }
  
  .nav-toggle,
  .globe-container,
  .campus-gallery,
  .apply-button-container {
    display: none;
  }
  
  .hero {
    height: auto;
    page-break-after: always;
    background: var(--white);
  }
  
  .hero-content {
    width: 100%;
  }
  
  .info-card:hover {
    transform: none;
  }
  
  .info-card-back {
    transform: none;
    position: static;
  }
  
  .cta-button {
    border: 2px solid var(--primary-color);
    background: var(--white);
    color: var(--primary-color);
  }
}
