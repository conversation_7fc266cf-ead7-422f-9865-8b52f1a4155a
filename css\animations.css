/* Animations */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes popupFadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes popupFadeOut {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.8);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes shine {
  0% {
    background-position: -200% center;
  }
  100% {
    background-position: 200% center;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .hero h1 {
    font-size: 3rem;
  }
  
  .hero h2 {
    font-size: 1.8rem;
  }
  
  .info-cards-container {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }
}

@media (max-width: 768px) {
  header {
    padding: 1rem 3%;
    flex-direction: column;
    gap: 1rem;
  }
  
  nav ul {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  nav ul li {
    margin: 0 1rem;
  }
  
  .hero {
    flex-direction: column;
    text-align: center;
    padding: 2rem 3%;
    height: auto;
    min-height: 100vh;
  }
  
  .hero-content {
    width: 100%;
    margin-bottom: 2rem;
  }
  
  .hero h1 {
    font-size: 2.5rem;
  }
  
  .hero h2 {
    font-size: 1.5rem;
  }
  
  .globe-container {
    width: 100%;
    height: 400px;
  }
  
  .info-section,
  .campus-section,
  .why-fefu-section,
  .apply-section {
    padding: 3rem 3% 2rem;
  }
  
  .info-section h2,
  .campus-section h2,
  .why-fefu-section h2,
  .apply-section h2 {
    font-size: 2rem;
  }
  
  .info-cards-container {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .info-card {
    height: 250px;
  }
  
  .advantages-container {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .campus-gallery {
    height: 300px;
    margin: 2rem auto;
  }
  
  .application-form {
    margin: 2rem 1rem;
    padding: 2rem;
  }
  
  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .hero h1 {
    font-size: 2rem;
  }
  
  .hero h2 {
    font-size: 1.3rem;
  }
  
  .hero p {
    font-size: 1rem;
  }
  
  .info-card {
    height: 220px;
  }
  
  .info-card-front,
  .info-card-back {
    padding: 1.5rem;
  }
  
  .advantage-item {
    padding: 1.5rem;
  }
  
  .advantage-icon {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }
  
  .application-form {
    padding: 1.5rem;
  }
}

/* Loading states */
.loading {
  opacity: 0.7;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--primary-color);
  border-radius: 50%;
  border-top-color: transparent;
  animation: rotate 1s linear infinite;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  :root {
    --primary-color: #000080;
    --secondary-color: #ffa500;
    --text-color: #000000;
    --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.3);
    --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.4);
    --shadow-heavy: 0 8px 30px rgba(0, 0, 0, 0.5);
  }
}

/* Print styles */
@media print {
  header {
    position: static;
    box-shadow: none;
  }
  
  .globe-container,
  .campus-gallery,
  .apply-button-container {
    display: none;
  }
  
  .hero {
    height: auto;
    page-break-after: always;
  }
  
  .info-card:hover {
    transform: none;
  }
  
  .info-card-back {
    transform: none;
    position: static;
  }
}
