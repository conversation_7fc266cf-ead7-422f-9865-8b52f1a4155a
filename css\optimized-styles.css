/* MBBS Vostrix - Optimized CSS Architecture */
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Open+Sans:wght@300;400;600;700&display=swap');

/* ===== CSS CUSTOM PROPERTIES ===== */
:root {
  /* Color System */
  --primary-color: #0056b3;
  --primary-dark: #0041a3;
  --primary-light: #1976d2;
  --secondary-color: #ffd700;
  --secondary-dark: #e6c200;
  --text-color: #333;
  --text-light: #666;
  --text-muted: #999;
  --light-bg: #f8f9fa;
  --white: #ffffff;
  --black: #000000;
  --error-color: #dc3545;
  --success-color: #28a745;
  --warning-color: #ffc107;
  
  /* Dark mode colors */
  --dark-bg: #1a1a1a;
  --dark-surface: #2d2d2d;
  --dark-text: #e0e0e0;
  --dark-text-secondary: #b0b0b0;
  
  /* Shadows */
  --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.15);
  --shadow-heavy: 0 8px 30px rgba(0, 0, 0, 0.2);
  --shadow-focus: 0 0 0 3px rgba(0, 86, 179, 0.2);
  
  /* Transitions */
  --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-smooth: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;
  
  /* Typography */
  --font-family-primary: 'Open Sans', sans-serif;
  --font-family-heading: 'Roboto', sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 2rem;
  --font-size-4xl: 2.5rem;
  --font-size-5xl: 3rem;
  
  /* Layout */
  --border-radius: 12px;
  --border-radius-sm: 6px;
  --border-radius-lg: 16px;
  --container-max-width: 1200px;
  --header-height: 80px;
  
  /* Z-index scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --text-color: var(--dark-text);
    --text-light: var(--dark-text-secondary);
    --light-bg: var(--dark-surface);
    --white: var(--dark-bg);
  }
}

[data-theme="dark"] {
  --text-color: var(--dark-text);
  --text-light: var(--dark-text-secondary);
  --light-bg: var(--dark-surface);
  --white: var(--dark-bg);
}

/* ===== RESET & BASE STYLES ===== */
*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-family-primary);
  line-height: 1.6;
  color: var(--text-color);
  background-color: var(--white);
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== FOCUS MANAGEMENT ===== */
:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

:focus:not(:focus-visible) {
  outline: none;
}

:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* ===== SKIP LINK ===== */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary-color);
  color: var(--white);
  padding: 8px;
  text-decoration: none;
  border-radius: var(--border-radius-sm);
  z-index: var(--z-tooltip);
  transition: var(--transition-fast);
}

.skip-link:focus {
  top: 6px;
}

/* ===== HEADER ===== */
header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) 5%;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-light);
  position: fixed;
  width: 100%;
  height: var(--header-height);
  z-index: var(--z-fixed);
  transition: var(--transition-smooth);
  will-change: transform;
}

.logo {
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--primary-color);
  font-family: var(--font-family-heading);
  text-decoration: none;
  transition: var(--transition-fast);
}

.logo:hover {
  color: var(--primary-dark);
  transform: scale(1.05);
}

/* ===== NAVIGATION ===== */
nav ul {
  display: flex;
  list-style: none;
  gap: var(--spacing-xl);
}

nav ul li a {
  text-decoration: none;
  color: var(--text-color);
  font-weight: 600;
  transition: var(--transition-smooth);
  position: relative;
  padding: var(--spacing-sm) 0;
}

nav ul li a:hover,
nav ul li a:focus {
  color: var(--primary-color);
}

nav ul li a::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: 0;
  left: 0;
  background-color: var(--primary-color);
  transition: var(--transition-smooth);
}

nav ul li a:hover::after,
nav ul li a:focus::after {
  width: 100%;
}

/* Mobile menu toggle */
.nav-toggle {
  display: none;
  background: none;
  border: none;
  font-size: var(--font-size-xl);
  color: var(--primary-color);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  transition: var(--transition-fast);
}

.nav-toggle:hover,
.nav-toggle:focus {
  background-color: var(--light-bg);
}

/* ===== HERO SECTION ===== */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  padding: var(--header-height) 5% 0;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, var(--light-bg) 0%, #e9ecef 100%);
}

.hero-content {
  width: 50%;
  z-index: 2;
  animation: slideInLeft 1s ease-out;
}

.hero h1 {
  font-size: var(--font-size-5xl);
  margin-bottom: var(--spacing-md);
  font-family: var(--font-family-heading);
  font-weight: 700;
  color: var(--primary-color);
  line-height: 1.2;
}

.hero h2 {
  font-size: var(--font-size-3xl);
  margin-bottom: var(--spacing-md);
  color: var(--secondary-color);
  font-weight: 600;
}

.hero p {
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-xl);
  color: var(--text-color);
  max-width: 500px;
  line-height: 1.7;
}

/* ===== HERO HIGHLIGHTS ===== */
.hero-highlights {
  display: flex;
  gap: var(--spacing-xl);
  margin: var(--spacing-xl) 0;
  flex-wrap: wrap;
}

.highlight-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  background: rgba(255, 255, 255, 0.1);
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--border-radius);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: var(--transition-smooth);
  will-change: transform;
}

.highlight-item:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px) translateZ(0);
}

.highlight-item i {
  color: var(--secondary-color);
  font-size: var(--font-size-lg);
}

.highlight-item span {
  font-weight: 600;
  color: var(--primary-color);
}

/* ===== CTA BUTTONS ===== */
.hero-cta {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-xl);
  flex-wrap: wrap;
}

.cta-button {
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--border-radius);
  text-decoration: none;
  font-weight: 600;
  font-size: var(--font-size-lg);
  transition: var(--transition-smooth);
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  position: relative;
  overflow: hidden;
  will-change: transform;
}

.cta-button.primary {
  background: var(--primary-color);
  color: var(--white);
  box-shadow: var(--shadow-medium);
}

.cta-button.primary:hover,
.cta-button.primary:focus {
  background: var(--primary-dark);
  transform: translateY(-3px) translateZ(0);
  box-shadow: var(--shadow-heavy);
}

.cta-button.secondary {
  background: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.cta-button.secondary:hover,
.cta-button.secondary:focus {
  background: var(--primary-color);
  color: var(--white);
}

/* ===== GLOBE CONTAINER ===== */
.globe-container {
  width: 50%;
  height: 100%;
  position: relative;
}

.globe-container canvas {
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-medium);
  will-change: transform;
}

.globe-info {
  position: absolute;
  bottom: var(--spacing-md);
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 86, 179, 0.9);
  color: var(--white);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: 20px;
  font-size: var(--font-size-sm);
  animation: pulse 2s infinite;
  backdrop-filter: blur(5px);
}

/* ===== LOADING STATES ===== */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading-shimmer 1.5s infinite;
}

.component-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  background: var(--light-bg);
  border-radius: var(--border-radius);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--light-bg);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* ===== ANIMATIONS ===== */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes loading-shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* ===== HIGH CONTRAST MODE ===== */
@media (prefers-contrast: high) {
  :root {
    --primary-color: #000080;
    --secondary-color: #ffa500;
    --text-color: #000000;
    --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.3);
    --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.4);
    --shadow-heavy: 0 8px 30px rgba(0, 0, 0, 0.5);
  }
}
