/* Hero Section Enhancements */
.hero-highlights {
  display: flex;
  gap: 2rem;
  margin: 2rem 0;
  flex-wrap: wrap;
}

.highlight-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.8rem 1.2rem;
  border-radius: var(--border-radius);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: var(--transition-smooth);
}

.highlight-item:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.highlight-item i {
  color: var(--secondary-color);
  font-size: 1.2rem;
}

.highlight-item span {
  font-weight: 600;
  color: var(--primary-color);
}

.hero-cta {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
  flex-wrap: wrap;
}

.cta-button {
  padding: 1rem 2rem;
  border-radius: var(--border-radius);
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  transition: var(--transition-smooth);
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  overflow: hidden;
}

.cta-button.primary {
  background: var(--primary-color);
  color: white;
  box-shadow: var(--shadow-medium);
}

.cta-button.primary:hover {
  background: #0041a3;
  transform: translateY(-3px);
  box-shadow: var(--shadow-heavy);
}

.cta-button.secondary {
  background: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.cta-button.secondary:hover {
  background: var(--primary-color);
  color: white;
}

.globe-info {
  position: absolute;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 86, 179, 0.9);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  animation: pulse 2s infinite;
}

/* Section Stats */
.section-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 2rem;
  margin: 3rem 0;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.stat-item {
  text-align: center;
  padding: 1.5rem;
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  transition: var(--transition-smooth);
}

.stat-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-medium);
}

.stat-number {
  display: block;
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-color);
  font-family: 'Roboto', sans-serif;
}

.stat-label {
  display: block;
  font-size: 0.9rem;
  color: #666;
  margin-top: 0.5rem;
  font-weight: 600;
}

/* Campus Features */
.campus-features {
  margin: 3rem 0;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  max-width: 1000px;
  margin: 0 auto;
}

.feature-item {
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  transition: var(--transition-smooth);
}

.feature-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-medium);
}

.feature-item i {
  font-size: 2.5rem;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.feature-item h4 {
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  color: var(--primary-color);
  font-weight: 600;
}

.feature-item p {
  font-size: 0.95rem;
  color: #666;
  line-height: 1.5;
}

/* Gallery Navigation */
.gallery-navigation {
  position: absolute;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 0.5rem;
  z-index: 10;
}

.gallery-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
  background: transparent;
  cursor: pointer;
  transition: var(--transition-smooth);
}

.gallery-dot:hover,
.gallery-dot.active {
  background: var(--secondary-color);
  border-color: var(--secondary-color);
}

/* Fallback Gallery */
.fallback-gallery {
  width: 100%;
  height: 100%;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
  height: 100%;
}

.gallery-item {
  position: relative;
  overflow: hidden;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-medium);
  transition: var(--transition-smooth);
}

.gallery-item:hover {
  transform: scale(1.02);
  box-shadow: var(--shadow-heavy);
}

.gallery-item img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.gallery-item-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  padding: 2rem 1rem 1rem;
}

.gallery-item-info h3 {
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
}

.gallery-item-info p {
  font-size: 0.9rem;
  opacity: 0.9;
}

/* Image Modal */
.image-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  animation: fadeIn 0.3s ease-out forwards;
}

.modal-content {
  position: relative;
  max-width: 90%;
  max-height: 90%;
  background: white;
  border-radius: var(--border-radius);
  overflow: hidden;
}

.modal-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  font-size: 1.5rem;
  cursor: pointer;
  z-index: 10;
}

.modal-content img {
  width: 100%;
  height: auto;
  display: block;
}

.modal-info {
  padding: 1.5rem;
}

.modal-info h3 {
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

/* Button Text Overlay */
.button-text-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: white;
  z-index: 10;
  pointer-events: none;
}

.button-text-overlay h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  font-weight: 700;
}

.button-text-overlay p {
  font-size: 1rem;
  margin-bottom: 1rem;
  opacity: 0.9;
}

.button-cta {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--secondary-color);
}

/* Fallback Apply Button */
.fallback-apply-button {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: linear-gradient(135deg, var(--primary-color), #0041a3);
  border-radius: var(--border-radius);
  color: white;
  text-align: center;
}

.apply-button-content h3 {
  font-size: 1.8rem;
  margin-bottom: 0.5rem;
}

.apply-button-content p {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.apply-btn-fallback {
  background: var(--secondary-color);
  color: var(--primary-color);
  border: none;
  padding: 1rem 2rem;
  border-radius: var(--border-radius);
  font-size: 1.2rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-smooth);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.apply-btn-fallback:hover {
  background: #e6c200;
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

/* Apply Message */
.apply-message {
  position: fixed;
  top: 2rem;
  right: 2rem;
  background: var(--primary-color);
  color: white;
  padding: 1rem 1.5rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-heavy);
  z-index: 1000;
  transform: translateX(100%);
  transition: transform 0.3s ease-out;
}

.apply-message.show {
  transform: translateX(0);
}

.message-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.message-content i {
  color: var(--secondary-color);
  font-size: 1.2rem;
}

/* Form Messages */
.form-message {
  padding: 1rem;
  border-radius: var(--border-radius);
  margin-bottom: 1rem;
  font-weight: 600;
}

.form-message.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.form-message.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* Global Loader */
.global-loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(5px);
}

.loader-content {
  text-align: center;
}

.loader-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.global-loader.fade-out {
  opacity: 0;
  transition: opacity 0.5s ease-out;
}

/* Fallback Content */
.fallback-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  color: #666;
  padding: 2rem;
}

.fallback-content p {
  margin-bottom: 0.5rem;
}
