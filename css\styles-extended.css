/* Globe container */
.globe-container {
  width: 50%;
  height: 100%;
  position: relative;
}

.globe-container canvas {
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-medium);
}

/* Info popup for globe */
.info-popup {
  position: absolute;
  background: var(--primary-color);
  color: white;
  padding: 1rem 1.5rem;
  border-radius: var(--border-radius);
  font-weight: 600;
  box-shadow: var(--shadow-heavy);
  z-index: 10;
  animation: popupFadeIn 0.5s ease-out;
  max-width: 300px;
  text-align: center;
}

.info-popup.fade-out {
  animation: popupFadeOut 1s ease-out forwards;
}

/* Sections */
.info-section,
.campus-section,
.why-fefu-section,
.apply-section {
  padding: 5rem 5% 3rem;
  text-align: center;
}

.info-section h2,
.campus-section h2,
.why-fefu-section h2,
.apply-section h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: var(--primary-color);
  font-family: 'Roboto', sans-serif;
  font-weight: 700;
}

.info-section p,
.campus-section p,
.why-fefu-section p,
.apply-section p {
  font-size: 1.1rem;
  margin-bottom: 3rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  color: #666;
}

/* 3D Info Cards */
.info-cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
  perspective: 1000px;
}

.info-card {
  height: 300px;
  position: relative;
  transform-style: preserve-3d;
  transition: transform 0.6s;
  cursor: pointer;
}

.info-card:hover {
  transform: rotateY(180deg);
}

.info-card-front,
.info-card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: var(--border-radius);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  box-shadow: var(--shadow-medium);
}

.info-card-front {
  background: linear-gradient(135deg, var(--primary-color), #0041a3);
  color: white;
}

.info-card-back {
  background: white;
  color: var(--text-color);
  transform: rotateY(180deg);
  border: 2px solid var(--primary-color);
}

.info-card-front h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  font-weight: 700;
}

.info-card-front .card-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: var(--secondary-color);
}

.info-card-back h4 {
  font-size: 1.3rem;
  margin-bottom: 1rem;
  color: var(--primary-color);
  font-weight: 600;
}

.info-card-back p {
  font-size: 1rem;
  line-height: 1.5;
  margin-bottom: 0.5rem;
}

.info-card-back .highlight {
  color: var(--primary-color);
  font-weight: 600;
}

/* Campus Gallery */
.campus-gallery {
  height: 500px;
  position: relative;
  margin: 3rem auto;
  max-width: 1200px;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow-heavy);
}

/* Advantages Container */
.advantages-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.advantage-item {
  background: white;
  padding: 2rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-medium);
  transition: var(--transition-smooth);
  text-align: center;
  position: relative;
  overflow: hidden;
}

.advantage-item:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-heavy);
}

.advantage-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.1), transparent);
  transition: left 0.5s;
}

.advantage-item:hover::before {
  left: 100%;
}

.advantage-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 1rem;
  background: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: white;
  transition: var(--transition-smooth);
}

.advantage-item:hover .advantage-icon {
  background: var(--secondary-color);
  color: var(--primary-color);
  transform: scale(1.1);
}

.advantage-item h3 {
  font-size: 1.3rem;
  margin-bottom: 1rem;
  color: var(--primary-color);
  font-weight: 600;
}

.advantage-item p {
  font-size: 1rem;
  color: #666;
  line-height: 1.5;
}

/* Apply Button Container */
.apply-button-container {
  margin: 3rem auto;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Application Form */
.application-form {
  max-width: 600px;
  margin: 3rem auto;
  background: white;
  padding: 3rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-medium);
}

.application-form h3 {
  font-size: 2rem;
  margin-bottom: 2rem;
  color: var(--primary-color);
  text-align: center;
  font-family: 'Roboto', sans-serif;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--text-color);
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 1rem;
  border: 2px solid #e9ecef;
  border-radius: var(--border-radius);
  font-size: 1rem;
  transition: var(--transition-smooth);
  font-family: inherit;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 86, 179, 0.1);
}

.submit-btn {
  width: 100%;
  padding: 1rem 2rem;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-smooth);
}

.submit-btn:hover {
  background: #0041a3;
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

/* Footer */
footer {
  background: var(--text-color);
  color: white;
  padding: 3rem 5% 1rem;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-section h3 {
  font-size: 1.3rem;
  margin-bottom: 1rem;
  color: var(--secondary-color);
}

.footer-section p {
  margin-bottom: 0.5rem;
  color: #ccc;
}

.social-icons {
  display: flex;
  gap: 1rem;
}

.social-icons a {
  color: white;
  font-size: 1.5rem;
  transition: var(--transition-smooth);
}

.social-icons a:hover {
  color: var(--secondary-color);
  transform: scale(1.2);
}

.copyright {
  text-align: center;
  padding-top: 2rem;
  border-top: 1px solid #555;
  color: #ccc;
}
