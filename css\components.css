/* ===== COMPONENT STYLES ===== */

/* Info Popup Enhancements */
.info-popup {
  position: absolute;
  background: var(--primary-color);
  color: var(--white);
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--border-radius);
  font-weight: 600;
  box-shadow: var(--shadow-heavy);
  z-index: var(--z-popover);
  animation: popupFadeIn 0.5s ease-out;
  max-width: 300px;
  text-align: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  will-change: transform, opacity;
}

.info-popup::before {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid var(--primary-color);
}

.info-popup.fade-out {
  animation: popupFadeOut 1s ease-out forwards;
}

/* Section Styles */
.section-padding {
  padding: var(--spacing-3xl) 5% var(--spacing-2xl);
  text-align: center;
}

.section-title {
  font-size: var(--font-size-4xl);
  margin-bottom: var(--spacing-md);
  color: var(--primary-color);
  font-family: var(--font-family-heading);
  font-weight: 700;
}

.section-description {
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-2xl);
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
  color: var(--text-light);
  line-height: 1.7;
}

/* Section Stats */
.section-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-xl);
  margin: var(--spacing-2xl) 0;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.stat-item {
  text-align: center;
  padding: var(--spacing-lg);
  background: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  transition: var(--transition-smooth);
  will-change: transform;
}

.stat-item:hover {
  transform: translateY(-5px) translateZ(0);
  box-shadow: var(--shadow-medium);
}

.stat-number {
  display: block;
  font-size: var(--font-size-4xl);
  font-weight: 700;
  color: var(--primary-color);
  font-family: var(--font-family-heading);
  line-height: 1;
}

.stat-label {
  display: block;
  font-size: var(--font-size-sm);
  color: var(--text-light);
  margin-top: var(--spacing-sm);
  font-weight: 600;
}

/* 3D Info Cards */
.info-cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-xl);
  margin-top: var(--spacing-2xl);
  perspective: 1000px;
}

.info-card {
  height: 300px;
  position: relative;
  transform-style: preserve-3d;
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  will-change: transform;
}

.info-card:hover,
.info-card:focus-within {
  transform: rotateY(180deg);
}

.info-card-front,
.info-card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: var(--border-radius);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-medium);
  will-change: transform;
}

.info-card-front {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: var(--white);
}

.info-card-back {
  background: var(--white);
  color: var(--text-color);
  transform: rotateY(180deg);
  border: 2px solid var(--primary-color);
}

.info-card-front h3 {
  font-size: var(--font-size-xl);
  margin-bottom: var(--spacing-md);
  font-weight: 700;
  text-align: center;
}

.info-card-front .card-icon {
  font-size: var(--font-size-5xl);
  margin-bottom: var(--spacing-md);
  color: var(--secondary-color);
  animation: float 3s ease-in-out infinite;
}

.info-card-back h4 {
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-md);
  color: var(--primary-color);
  font-weight: 600;
  text-align: center;
}

.info-card-back p {
  font-size: var(--font-size-base);
  line-height: 1.5;
  margin-bottom: var(--spacing-sm);
  text-align: center;
}

.info-card-back .highlight {
  color: var(--primary-color);
  font-weight: 600;
}

.card-hint {
  position: absolute;
  bottom: var(--spacing-md);
  left: 50%;
  transform: translateX(-50%);
  font-size: var(--font-size-sm);
  opacity: 0.8;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.card-hint i {
  animation: rotate 2s linear infinite;
}

/* Campus Features */
.campus-features {
  margin: var(--spacing-2xl) 0;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-xl);
  max-width: 1000px;
  margin: 0 auto;
}

.feature-item {
  text-align: center;
  padding: var(--spacing-xl);
  background: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  transition: var(--transition-smooth);
  position: relative;
  overflow: hidden;
  will-change: transform;
}

.feature-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(0, 86, 179, 0.05), transparent);
  transition: left var(--transition-slow);
}

.feature-item:hover {
  transform: translateY(-5px) translateZ(0);
  box-shadow: var(--shadow-medium);
}

.feature-item:hover::before {
  left: 100%;
}

.feature-item i {
  font-size: var(--font-size-4xl);
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
  display: block;
}

.feature-item h4 {
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-sm);
  color: var(--primary-color);
  font-weight: 600;
}

.feature-item p {
  font-size: var(--font-size-base);
  color: var(--text-light);
  line-height: 1.6;
}

/* Campus Gallery */
.campus-gallery {
  height: 500px;
  position: relative;
  margin: var(--spacing-2xl) auto;
  max-width: 1200px;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow-heavy);
  background: var(--light-bg);
}

.gallery-navigation {
  position: absolute;
  bottom: var(--spacing-md);
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: var(--spacing-sm);
  z-index: var(--z-dropdown);
  background: rgba(0, 0, 0, 0.5);
  padding: var(--spacing-sm);
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

.gallery-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid var(--white);
  background: transparent;
  cursor: pointer;
  transition: var(--transition-smooth);
  min-width: 44px; /* Touch target */
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.gallery-dot::before {
  content: '';
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: transparent;
  transition: var(--transition-smooth);
}

.gallery-dot:hover::before,
.gallery-dot.active::before {
  background: var(--secondary-color);
}

.gallery-dot:focus {
  outline: 2px solid var(--secondary-color);
  outline-offset: 2px;
}

/* Advantages Container */
.advantages-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-xl);
  margin-top: var(--spacing-2xl);
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.advantage-item {
  background: var(--white);
  padding: var(--spacing-xl);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-medium);
  transition: var(--transition-smooth);
  text-align: center;
  position: relative;
  overflow: hidden;
  will-change: transform;
}

.advantage-item:hover {
  transform: translateY(-10px) translateZ(0);
  box-shadow: var(--shadow-heavy);
}

.advantage-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.1), transparent);
  transition: left var(--transition-slow);
}

.advantage-item:hover::before {
  left: 100%;
}

.advantage-icon-container {
  margin-bottom: var(--spacing-md);
}

.advantage-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto var(--spacing-md);
  background: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-3xl);
  color: var(--white);
  transition: var(--transition-smooth);
  position: relative;
  will-change: transform;
}

.advantage-item:hover .advantage-icon {
  background: var(--secondary-color);
  color: var(--primary-color);
  transform: scale(1.1) rotateY(15deg);
}

.advantage-item h3 {
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-md);
  color: var(--primary-color);
  font-weight: 600;
}

.advantage-item p {
  font-size: var(--font-size-base);
  color: var(--text-light);
  line-height: 1.6;
}

/* Apply Button Container */
.apply-button-container {
  margin: var(--spacing-2xl) auto;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: 600px;
}

/* Fallback Styles */
.fallback-globe {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: linear-gradient(135deg, var(--light-bg), #e9ecef);
  border-radius: var(--border-radius);
}

.globe-placeholder {
  text-align: center;
  padding: var(--spacing-2xl);
}

.earth-icon {
  font-size: 4rem;
  margin-bottom: var(--spacing-md);
  animation: float 3s ease-in-out infinite;
}

.globe-placeholder h3 {
  font-size: var(--font-size-xl);
  margin-bottom: var(--spacing-sm);
  color: var(--primary-color);
}

.globe-placeholder p {
  margin-bottom: var(--spacing-lg);
  color: var(--text-light);
}

.explore-btn {
  background: var(--primary-color);
  color: var(--white);
  border: none;
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--border-radius);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-smooth);
}

.explore-btn:hover,
.explore-btn:focus {
  background: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.location-info {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--primary-color);
  color: var(--white);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius);
  text-align: center;
  box-shadow: var(--shadow-heavy);
  animation: popupFadeIn 0.5s ease-out;
}

.location-info.fade-out {
  animation: popupFadeOut 1s ease-out forwards;
}

/* Form Enhancements */
.application-form {
  max-width: 600px;
  margin: var(--spacing-2xl) auto;
  background: var(--white);
  padding: var(--spacing-2xl);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-medium);
}

.application-form h3 {
  font-size: var(--font-size-3xl);
  margin-bottom: var(--spacing-xl);
  color: var(--primary-color);
  text-align: center;
  font-family: var(--font-family-heading);
}

.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-sm);
  font-weight: 600;
  color: var(--text-color);
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: var(--spacing-md);
  border: 2px solid #e9ecef;
  border-radius: var(--border-radius);
  font-size: var(--font-size-base);
  transition: var(--transition-smooth);
  font-family: inherit;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: var(--shadow-focus);
}

.form-group input.error,
.form-group textarea.error {
  border-color: var(--error-color);
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.2);
}

.field-error {
  color: var(--error-color);
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-xs);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.field-error::before {
  content: '⚠';
  font-size: var(--font-size-base);
}

.submit-btn {
  width: 100%;
  padding: var(--spacing-md) var(--spacing-xl);
  background: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--font-size-lg);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-smooth);
  will-change: transform;
}

.submit-btn:hover,
.submit-btn:focus {
  background: var(--primary-dark);
  transform: translateY(-2px) translateZ(0);
  box-shadow: var(--shadow-medium);
}

.submit-btn:disabled {
  background: var(--text-muted);
  cursor: not-allowed;
  transform: none;
}

/* Form Messages */
.form-message {
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-md);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  animation: slideInDown 0.3s ease-out;
}

.form-message.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.form-message.success::before {
  content: '✓';
  color: var(--success-color);
}

.form-message.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.form-message.error::before {
  content: '✗';
  color: var(--error-color);
}

.form-message.fade-out {
  animation: slideOutUp 0.3s ease-out forwards;
}

/* Global Loader */
.global-loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  backdrop-filter: blur(5px);
}

.loader-content {
  text-align: center;
}

.loader-content p {
  margin-top: var(--spacing-md);
  color: var(--text-light);
  font-weight: 600;
}

.global-loader.fade-out {
  opacity: 0;
  transition: opacity var(--transition-slow);
}

/* Fallback Content */
.fallback-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  color: var(--text-light);
  padding: var(--spacing-2xl);
  background: var(--light-bg);
  border-radius: var(--border-radius);
}

.fallback-content p {
  margin-bottom: var(--spacing-sm);
  font-size: var(--font-size-base);
}

/* Animation Classes */
.animate-in {
  animation: fadeInUp 0.8s ease-out forwards;
}

/* Keyframe Animations */
@keyframes popupFadeIn {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes popupFadeOut {
  from {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
  to {
    opacity: 0;
    transform: scale(0.8) translateY(-20px);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideOutUp {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-20px);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
