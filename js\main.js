import { createGlobe } from './globe.js';
import { createInfoCards } from './infoCards.js';
import { createCampusGallery } from './campusGallery.js';
import { createAdvantages } from './advantages.js';
import { createApplyButton } from './applyButton.js';

// Main application class
class MBBSVostrixApp {
  constructor() {
    this.components = {};
    this.isLoaded = false;
    this.isMobile = window.innerWidth <= 768;
    
    this.init();
  }

  async init() {
    try {
      // Show loading state
      this.showLoading();
      
      // Initialize components based on device capabilities
      await this.initializeComponents();
      
      // Setup event listeners
      this.setupEventListeners();
      
      // Setup intersection observer for animations
      this.setupIntersectionObserver();
      
      // Hide loading state
      this.hideLoading();
      
      this.isLoaded = true;
      console.log('MBBS Vostrix website loaded successfully');
      
    } catch (error) {
      console.error('Error initializing website:', error);
      this.showFallback();
    }
  }

  async initializeComponents() {
    // Initialize globe
    const globeContainer = document.getElementById('globe-container');
    if (globeContainer) {
      this.components.globe = createGlobe(globeContainer);
    }

    // Initialize info cards
    const infoCardsContainer = document.getElementById('info-cards-container');
    if (infoCardsContainer) {
      this.components.infoCards = createInfoCards(infoCardsContainer);
    }

    // Initialize campus gallery
    const campusGallery = document.getElementById('campus-gallery');
    if (campusGallery) {
      this.components.campusGallery = await createCampusGallery(campusGallery);
    }

    // Initialize advantages section
    const advantagesContainer = document.getElementById('advantages-container');
    if (advantagesContainer) {
      this.components.advantages = createAdvantages(advantagesContainer);
    }

    // Initialize apply button
    const applyButtonContainer = document.getElementById('apply-button-container');
    if (applyButtonContainer) {
      this.components.applyButton = createApplyButton(applyButtonContainer);
    }
  }

  setupEventListeners() {
    // Handle window resize
    window.addEventListener('resize', this.handleResize.bind(this));
    
    // Handle form submission
    const enquiryForm = document.getElementById('enquiry-form');
    if (enquiryForm) {
      enquiryForm.addEventListener('submit', this.handleFormSubmit.bind(this));
    }
    
    // Handle navigation
    this.setupSmoothScrolling();
    
    // Handle mobile menu if needed
    if (this.isMobile) {
      this.setupMobileNavigation();
    }
  }

  setupIntersectionObserver() {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in');
          
          // Trigger component-specific animations
          const componentId = entry.target.id;
          if (this.components[componentId] && this.components[componentId].animate) {
            this.components[componentId].animate();
          }
        }
      });
    }, observerOptions);

    // Observe all major sections
    const sections = document.querySelectorAll('section');
    sections.forEach(section => {
      observer.observe(section);
    });
  }

  setupSmoothScrolling() {
    const navLinks = document.querySelectorAll('nav a[href^="#"]');
    
    navLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault();
        
        const targetId = link.getAttribute('href').substring(1);
        const targetElement = document.getElementById(targetId);
        
        if (targetElement) {
          const headerHeight = document.querySelector('header').offsetHeight;
          const targetPosition = targetElement.offsetTop - headerHeight;
          
          window.scrollTo({
            top: targetPosition,
            behavior: 'smooth'
          });
        }
      });
    });
  }

  setupMobileNavigation() {
    // Add mobile menu toggle if needed
    const nav = document.querySelector('nav');
    const navToggle = document.createElement('button');
    navToggle.className = 'nav-toggle';
    navToggle.innerHTML = '<i class="fas fa-bars"></i>';
    navToggle.setAttribute('aria-label', 'Toggle navigation');
    
    const header = document.querySelector('header');
    header.insertBefore(navToggle, nav);
    
    navToggle.addEventListener('click', () => {
      nav.classList.toggle('nav-open');
      navToggle.classList.toggle('active');
    });
  }

  handleResize() {
    const newIsMobile = window.innerWidth <= 768;
    
    if (newIsMobile !== this.isMobile) {
      this.isMobile = newIsMobile;
      
      // Reinitialize components if needed
      if (this.components.globe && this.components.globe.handleResize) {
        this.components.globe.handleResize();
      }
      
      if (this.components.campusGallery && this.components.campusGallery.handleResize) {
        this.components.campusGallery.handleResize();
      }
    }
  }

  async handleFormSubmit(e) {
    e.preventDefault();
    
    const form = e.target;
    const formData = new FormData(form);
    const submitBtn = form.querySelector('.submit-btn');
    
    // Show loading state
    submitBtn.textContent = 'Submitting...';
    submitBtn.disabled = true;
    
    try {
      // Simulate form submission (replace with actual endpoint)
      await this.submitForm(formData);
      
      // Show success message
      this.showFormSuccess();
      form.reset();
      
    } catch (error) {
      console.error('Form submission error:', error);
      this.showFormError();
    } finally {
      // Reset button
      submitBtn.textContent = 'Submit Enquiry';
      submitBtn.disabled = false;
    }
  }

  async submitForm(formData) {
    // Simulate API call
    return new Promise((resolve) => {
      setTimeout(() => {
        console.log('Form submitted:', Object.fromEntries(formData));
        resolve();
      }, 1000);
    });
  }

  showFormSuccess() {
    const message = document.createElement('div');
    message.className = 'form-message success';
    message.textContent = 'Thank you! Your enquiry has been submitted successfully.';
    
    const form = document.getElementById('enquiry-form');
    form.parentNode.insertBefore(message, form);
    
    setTimeout(() => message.remove(), 5000);
  }

  showFormError() {
    const message = document.createElement('div');
    message.className = 'form-message error';
    message.textContent = 'Sorry, there was an error submitting your enquiry. Please try again.';
    
    const form = document.getElementById('enquiry-form');
    form.parentNode.insertBefore(message, form);
    
    setTimeout(() => message.remove(), 5000);
  }

  showLoading() {
    const loader = document.createElement('div');
    loader.id = 'global-loader';
    loader.className = 'global-loader';
    loader.innerHTML = `
      <div class="loader-content">
        <div class="loader-spinner"></div>
        <p>Loading MBBS Vostrix...</p>
      </div>
    `;
    document.body.appendChild(loader);
  }

  hideLoading() {
    const loader = document.getElementById('global-loader');
    if (loader) {
      loader.classList.add('fade-out');
      setTimeout(() => loader.remove(), 500);
    }
  }

  showFallback() {
    console.log('Showing fallback content for unsupported devices');
    
    // Hide 3D containers and show static alternatives
    const containers = [
      'globe-container',
      'campus-gallery',
      'apply-button-container'
    ];
    
    containers.forEach(id => {
      const container = document.getElementById(id);
      if (container) {
        container.innerHTML = `
          <div class="fallback-content">
            <p>Interactive content not available on this device.</p>
            <p>Please visit on a desktop browser for the full experience.</p>
          </div>
        `;
      }
    });
  }

  // Public methods for external access
  getComponent(name) {
    return this.components[name];
  }

  isComponentLoaded(name) {
    return !!this.components[name];
  }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.mbbsVostrixApp = new MBBSVostrixApp();
});

// Export for module usage
export default MBBSVostrixApp;
