export function createAdvantages(container) {
  const advantagesData = [
    {
      id: 'ranking',
      icon: '🏆',
      title: 'Top 5 in Russia',
      description: 'FEFU ranks among the top 5 universities in Russia and is featured in QS World University Rankings Top 500.',
      stats: 'QS Ranking: 493rd globally',
      color: '#FFD700'
    },
    {
      id: 'partnerships',
      icon: '🌍',
      title: 'Global Partnerships',
      description: 'Strong international collaborations with universities and medical institutions across 40+ countries.',
      stats: '40+ partner countries',
      color: '#0056b3'
    },
    {
      id: 'infrastructure',
      icon: '🏥',
      title: 'Modern Infrastructure',
      description: 'State-of-the-art medical facilities, simulation labs, and research centers equipped with latest technology.',
      stats: '15+ specialized labs',
      color: '#28a745'
    },
    {
      id: 'affordability',
      icon: '💰',
      title: 'Affordable Excellence',
      description: 'High-quality medical education at a fraction of the cost compared to private medical colleges.',
      stats: 'Save up to 70% on fees',
      color: '#dc3545'
    },
    {
      id: 'faculty',
      icon: '👨‍⚕️',
      title: 'Expert Faculty',
      description: 'Learn from experienced professors and practicing physicians with international medical expertise.',
      stats: '200+ qualified faculty',
      color: '#6f42c1'
    },
    {
      id: 'recognition',
      icon: '📜',
      title: 'Global Recognition',
      description: 'WHO and NMC recognized degree allows you to practice medicine worldwide after clearing respective exams.',
      stats: 'Practice in 180+ countries',
      color: '#fd7e14'
    }
  ];

  // Clear container
  container.innerHTML = '';

  // Create advantages grid
  advantagesData.forEach((advantage, index) => {
    const advantageElement = createAdvantageItem(advantage, index);
    container.appendChild(advantageElement);
  });

  // Setup animations
  setupAdvantageAnimations(container);

  return {
    advantages: advantagesData,
    container,
    animate: () => animateAdvantages(container)
  };
}

function createAdvantageItem(data, index) {
  const item = document.createElement('div');
  item.className = 'advantage-item';
  item.setAttribute('data-advantage-id', data.id);
  item.style.animationDelay = `${index * 0.15}s`;

  // Create 3D-style icon
  const iconContainer = document.createElement('div');
  iconContainer.className = 'advantage-icon-container';
  
  const icon = document.createElement('div');
  icon.className = 'advantage-icon';
  icon.style.setProperty('--icon-color', data.color);
  
  // Create layered icon effect for 3D appearance
  const iconLayers = document.createElement('div');
  iconLayers.className = 'icon-layers';
  iconLayers.innerHTML = `
    <div class="icon-layer icon-shadow">${data.icon}</div>
    <div class="icon-layer icon-main">${data.icon}</div>
    <div class="icon-layer icon-highlight">${data.icon}</div>
  `;
  
  icon.appendChild(iconLayers);
  iconContainer.appendChild(icon);

  // Create content
  const content = document.createElement('div');
  content.className = 'advantage-content';
  content.innerHTML = `
    <h3>${data.title}</h3>
    <p>${data.description}</p>
    <div class="advantage-stats">
      <span class="stats-badge">${data.stats}</span>
    </div>
  `;

  // Create floating particles effect
  const particles = document.createElement('div');
  particles.className = 'advantage-particles';
  for (let i = 0; i < 5; i++) {
    const particle = document.createElement('div');
    particle.className = 'particle';
    particle.style.animationDelay = `${Math.random() * 2}s`;
    particles.appendChild(particle);
  }

  item.appendChild(iconContainer);
  item.appendChild(content);
  item.appendChild(particles);

  // Add hover effects
  setupAdvantageHoverEffects(item, data);

  return item;
}

function setupAdvantageHoverEffects(item, data) {
  const icon = item.querySelector('.advantage-icon');
  const particles = item.querySelector('.advantage-particles');
  
  item.addEventListener('mouseenter', () => {
    // Animate icon
    icon.style.transform = 'scale(1.1) rotateY(15deg)';
    icon.style.boxShadow = `0 10px 30px ${data.color}40`;
    
    // Activate particles
    particles.classList.add('active');
    
    // Add ripple effect
    createRippleEffect(item, data.color);
  });

  item.addEventListener('mouseleave', () => {
    // Reset icon
    icon.style.transform = 'scale(1) rotateY(0deg)';
    icon.style.boxShadow = '';
    
    // Deactivate particles
    particles.classList.remove('active');
  });

  // Add click tracking
  item.addEventListener('click', () => {
    trackAdvantageInteraction(data.id);
    showAdvantageDetails(data);
  });
}

function createRippleEffect(element, color) {
  const ripple = document.createElement('div');
  ripple.className = 'ripple-effect';
  ripple.style.background = `radial-gradient(circle, ${color}20 0%, transparent 70%)`;
  
  element.appendChild(ripple);
  
  setTimeout(() => {
    ripple.remove();
  }, 600);
}

function setupAdvantageAnimations(container) {
  const items = container.querySelectorAll('.advantage-item');
  
  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry, index) => {
      if (entry.isIntersecting) {
        setTimeout(() => {
          entry.target.classList.add('animate-in');
          
          // Trigger icon animation
          const icon = entry.target.querySelector('.advantage-icon');
          if (icon) {
            icon.classList.add('bounce-in');
          }
        }, index * 150);
      }
    });
  }, {
    threshold: 0.3
  });

  items.forEach(item => {
    observer.observe(item);
  });
}

function animateAdvantages(container) {
  const items = container.querySelectorAll('.advantage-item');
  
  items.forEach((item, index) => {
    setTimeout(() => {
      item.style.animation = 'fadeInUp 0.8s ease-out forwards';
      
      // Add staggered icon animations
      const icon = item.querySelector('.advantage-icon');
      if (icon) {
        setTimeout(() => {
          icon.style.animation = 'pulse 0.6s ease-out';
        }, 200);
      }
    }, index * 100);
  });
}

function showAdvantageDetails(data) {
  const modal = document.createElement('div');
  modal.className = 'advantage-modal';
  modal.innerHTML = `
    <div class="modal-content">
      <button class="modal-close" aria-label="Close">&times;</button>
      <div class="modal-header">
        <div class="modal-icon" style="background: ${data.color}">
          ${data.icon}
        </div>
        <h2>${data.title}</h2>
      </div>
      <div class="modal-body">
        <p>${data.description}</p>
        <div class="modal-stats">
          <div class="stat-item">
            <span class="stat-label">Key Metric:</span>
            <span class="stat-value">${data.stats}</span>
          </div>
        </div>
        ${getAdditionalInfo(data.id)}
      </div>
    </div>
  `;

  document.body.appendChild(modal);

  // Setup modal events
  const closeBtn = modal.querySelector('.modal-close');
  closeBtn.addEventListener('click', () => modal.remove());
  
  modal.addEventListener('click', (e) => {
    if (e.target === modal) modal.remove();
  });

  // Animate modal in
  setTimeout(() => modal.classList.add('show'), 10);
}

function getAdditionalInfo(advantageId) {
  const additionalInfo = {
    ranking: `
      <div class="additional-info">
        <h4>University Rankings</h4>
        <ul>
          <li>QS World University Rankings: 493rd globally</li>
          <li>Times Higher Education: Top 800</li>
          <li>Academic Ranking of World Universities: Top 500</li>
          <li>Regional ranking: Top 5 in Far East Russia</li>
        </ul>
      </div>
    `,
    partnerships: `
      <div class="additional-info">
        <h4>International Partnerships</h4>
        <ul>
          <li>Medical universities in USA, UK, Germany</li>
          <li>Student exchange programs</li>
          <li>Joint research initiatives</li>
          <li>Clinical training opportunities abroad</li>
        </ul>
      </div>
    `,
    infrastructure: `
      <div class="additional-info">
        <h4>Facilities & Infrastructure</h4>
        <ul>
          <li>Modern simulation laboratories</li>
          <li>Digital anatomy tables</li>
          <li>Research centers with latest equipment</li>
          <li>24/7 library access</li>
        </ul>
      </div>
    `,
    affordability: `
      <div class="additional-info">
        <h4>Cost Comparison</h4>
        <ul>
          <li>FEFU: ₹3.8-4.95L per year</li>
          <li>Private colleges in India: ₹15-25L per year</li>
          <li>Other countries: ₹8-20L per year</li>
          <li>Total savings: Up to ₹80L over 6 years</li>
        </ul>
      </div>
    `,
    faculty: `
      <div class="additional-info">
        <h4>Faculty Excellence</h4>
        <ul>
          <li>PhD holders: 85% of faculty</li>
          <li>International experience: 60% of faculty</li>
          <li>Student-faculty ratio: 12:1</li>
          <li>English-speaking professors</li>
        </ul>
      </div>
    `,
    recognition: `
      <div class="additional-info">
        <h4>Global Recognition</h4>
        <ul>
          <li>WHO World Directory of Medical Schools</li>
          <li>NMC (National Medical Commission) approved</li>
          <li>WFME (World Federation for Medical Education) recognized</li>
          <li>Eligible for USMLE, PLAB, and other international exams</li>
        </ul>
      </div>
    `
  };

  return additionalInfo[advantageId] || '';
}

function trackAdvantageInteraction(advantageId) {
  console.log(`Advantage interaction: ${advantageId}`);
  
  // Analytics integration
  if (typeof gtag !== 'undefined') {
    gtag('event', 'advantage_click', {
      'advantage_id': advantageId,
      'event_category': 'engagement'
    });
  }
}

// Export utilities
export const advantageUtils = {
  showAdvantageDetails,
  trackAdvantageInteraction
};
