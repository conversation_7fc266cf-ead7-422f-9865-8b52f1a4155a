/* ===== ADVANCED COMPONENTS CSS ===== */

/* Enhanced Globe Styles */
.enhanced-info-popup {
  position: absolute;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: var(--white);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-heavy);
  z-index: var(--z-popover);
  animation: enhancedPopupFadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  max-width: 350px;
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.enhanced-info-popup .popup-content h3 {
  font-size: var(--font-size-xl);
  margin-bottom: var(--spacing-sm);
  color: var(--secondary-color);
  font-weight: 700;
}

.enhanced-info-popup .popup-content p {
  margin-bottom: var(--spacing-md);
  opacity: 0.9;
}

.enhanced-info-popup .popup-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.enhanced-info-popup .popup-details span {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-sm);
  background: rgba(255, 255, 255, 0.1);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
}

.enhanced-fallback-globe {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: linear-gradient(135deg, var(--light-bg), #e3f2fd);
  border-radius: var(--border-radius);
  position: relative;
  overflow: hidden;
}

.enhanced-fallback-globe::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(0, 86, 179, 0.1) 0%, transparent 70%);
  animation: rotate 20s linear infinite;
}

.enhanced-fallback-globe .globe-placeholder {
  text-align: center;
  padding: var(--spacing-2xl);
  position: relative;
  z-index: 2;
}

.enhanced-fallback-globe .earth-animation {
  font-size: 5rem;
  margin-bottom: var(--spacing-lg);
  animation: float 3s ease-in-out infinite;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

.enhanced-fallback-globe .location-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  margin: var(--spacing-lg) 0;
}

.enhanced-fallback-globe .location-details span {
  background: var(--white);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  font-weight: 600;
  color: var(--primary-color);
}

.explore-btn.enhanced {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: var(--white);
  border: none;
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--border-radius);
  font-weight: 600;
  font-size: var(--font-size-lg);
  cursor: pointer;
  transition: var(--transition-smooth);
  box-shadow: var(--shadow-medium);
  position: relative;
  overflow: hidden;
}

.explore-btn.enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-slow);
}

.explore-btn.enhanced:hover::before {
  left: 100%;
}

.explore-btn.enhanced:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-heavy);
}

.enhanced-location-info {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--white);
  padding: var(--spacing-2xl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-heavy);
  animation: enhancedPopupFadeIn 0.6s ease-out;
  max-width: 400px;
  width: 90%;
}

.enhanced-location-info .info-content h4 {
  color: var(--primary-color);
  font-size: var(--font-size-2xl);
  margin-bottom: var(--spacing-sm);
  text-align: center;
}

.enhanced-location-info .info-content p {
  text-align: center;
  margin-bottom: var(--spacing-lg);
  color: var(--text-light);
}

.enhanced-location-info .info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-md);
}

.enhanced-location-info .info-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--light-bg);
  border-radius: var(--border-radius);
}

.enhanced-location-info .info-item .icon {
  font-size: var(--font-size-xl);
}

/* Advanced Form Styles */
.advanced-form {
  position: relative;
}

.form-progress {
  margin-bottom: var(--spacing-2xl);
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: var(--light-bg);
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: var(--spacing-lg);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  border-radius: 3px;
  transition: width var(--transition-smooth);
  width: 25%;
}

.step-indicators {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.step-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  flex: 1;
  position: relative;
}

.step-indicator:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 20px;
  left: 60%;
  width: 80%;
  height: 2px;
  background: var(--light-bg);
  z-index: -1;
}

.step-indicator.completed:not(:last-child)::after {
  background: var(--primary-color);
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--light-bg);
  color: var(--text-light);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  transition: var(--transition-smooth);
}

.step-indicator.active .step-number {
  background: var(--primary-color);
  color: var(--white);
}

.step-indicator.completed .step-number {
  background: var(--primary-color);
  color: var(--white);
}

.step-indicator.completed .step-number::before {
  content: '✓';
  font-size: var(--font-size-sm);
}

.step-label {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--text-light);
  text-align: center;
}

.step-indicator.active .step-label,
.step-indicator.completed .step-label {
  color: var(--primary-color);
}

.form-step {
  display: none;
  animation: slideInRight 0.3s ease-out;
}

.form-step h4 {
  color: var(--primary-color);
  font-size: var(--font-size-2xl);
  margin-bottom: var(--spacing-lg);
  text-align: center;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-lg);
}

.form-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--spacing-2xl);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--light-bg);
}

.btn-prev,
.btn-next,
.btn-submit {
  padding: var(--spacing-md) var(--spacing-xl);
  border: none;
  border-radius: var(--border-radius);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-smooth);
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.btn-prev {
  background: var(--light-bg);
  color: var(--text-color);
}

.btn-prev:hover:not(:disabled) {
  background: #e0e0e0;
}

.btn-prev:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-next,
.btn-submit {
  background: var(--primary-color);
  color: var(--white);
}

.btn-next:hover,
.btn-submit:hover {
  background: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

/* File Upload Styles */
.file-upload-container {
  margin-top: var(--spacing-sm);
}

.file-drop-zone {
  border: 2px dashed var(--primary-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-2xl);
  text-align: center;
  background: rgba(0, 86, 179, 0.05);
  transition: var(--transition-smooth);
  cursor: pointer;
}

.file-drop-zone:hover,
.file-drop-zone.drag-over {
  border-color: var(--secondary-color);
  background: rgba(255, 215, 0, 0.1);
}

.drop-zone-content i {
  font-size: var(--font-size-4xl);
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
}

.drop-zone-content p {
  margin-bottom: var(--spacing-sm);
  color: var(--text-color);
}

.drop-zone-content .browse-link {
  color: var(--primary-color);
  text-decoration: underline;
  cursor: pointer;
  font-weight: 600;
}

.drop-zone-content small {
  display: block;
  color: var(--text-light);
  margin-bottom: var(--spacing-xs);
}

.uploaded-files-list {
  margin-top: var(--spacing-md);
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md);
  background: var(--white);
  border: 1px solid var(--light-bg);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-sm);
  transition: var(--transition-smooth);
}

.file-item:hover {
  box-shadow: var(--shadow-light);
}

.file-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  flex: 1;
}

.file-info i {
  color: var(--primary-color);
  font-size: var(--font-size-lg);
}

.file-details {
  flex: 1;
}

.file-name {
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: var(--spacing-xs);
}

.file-size {
  font-size: var(--font-size-sm);
  color: var(--text-light);
}

.file-progress {
  flex: 1;
  margin: 0 var(--spacing-md);
}

.file-progress .progress-bar {
  width: 100%;
  height: 4px;
  background: var(--light-bg);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: var(--spacing-xs);
}

.file-progress .progress-fill {
  height: 100%;
  background: var(--primary-color);
  border-radius: 2px;
  transition: width var(--transition-smooth);
}

.file-progress .progress-text {
  font-size: var(--font-size-xs);
  color: var(--text-light);
  text-align: center;
}

.upload-success {
  color: var(--success-color);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-sm);
}

.upload-error {
  color: var(--error-color);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--font-size-sm);
}

.file-remove {
  background: none;
  border: none;
  color: var(--text-light);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--border-radius-sm);
  transition: var(--transition-smooth);
}

.file-remove:hover {
  background: var(--error-color);
  color: var(--white);
}

.file-item.upload-success {
  border-color: var(--success-color);
  background: rgba(40, 167, 69, 0.05);
}

.file-item.upload-error {
  border-color: var(--error-color);
  background: rgba(220, 53, 69, 0.05);
}

/* Checkbox Styles */
.checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  cursor: pointer;
  line-height: 1.5;
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid var(--primary-color);
  border-radius: var(--border-radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition-smooth);
  flex-shrink: 0;
  margin-top: 2px;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
  background: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  color: var(--white);
  font-size: var(--font-size-sm);
  font-weight: 600;
}

/* Auto-save Indicator */
.auto-save-indicator {
  position: fixed;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  background: var(--success-color);
  color: var(--white);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
  font-weight: 600;
  z-index: var(--z-tooltip);
  animation: slideInRight 0.3s ease-out;
}

.auto-save-indicator.fade-out {
  animation: slideOutRight 0.3s ease-out forwards;
}

/* Restore Notification */
.restore-notification {
  background: var(--warning-color);
  color: var(--black);
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.notification-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.clear-saved-data {
  background: none;
  border: 1px solid var(--black);
  color: var(--black);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  font-size: var(--font-size-sm);
  transition: var(--transition-smooth);
}

.clear-saved-data:hover {
  background: var(--black);
  color: var(--warning-color);
}

/* PWA Notifications */
.update-notification,
.install-banner {
  position: fixed;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  background: var(--primary-color);
  color: var(--white);
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-heavy);
  z-index: var(--z-tooltip);
  animation: slideInRight 0.3s ease-out;
  max-width: 300px;
}

.install-banner {
  background: var(--secondary-color);
  color: var(--black);
}

.update-notification .notification-content,
.install-banner .banner-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

.update-notification button,
.install-banner button {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: inherit;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  font-size: var(--font-size-sm);
  transition: var(--transition-smooth);
}

.update-notification button:hover,
.install-banner button:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Animations */
@keyframes enhancedPopupFadeIn {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideOutRight {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(30px);
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  
  .step-indicators {
    flex-wrap: wrap;
    gap: var(--spacing-sm);
  }
  
  .step-indicator {
    flex: 0 0 auto;
    min-width: 80px;
  }
  
  .step-indicator:not(:last-child)::after {
    display: none;
  }
  
  .form-navigation {
    flex-direction: column;
    gap: var(--spacing-md);
  }
  
  .btn-prev,
  .btn-next,
  .btn-submit {
    width: 100%;
    justify-content: center;
  }
  
  .update-notification,
  .install-banner {
    position: fixed;
    top: auto;
    bottom: var(--spacing-lg);
    left: var(--spacing-md);
    right: var(--spacing-md);
    max-width: none;
  }
}
